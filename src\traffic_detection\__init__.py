"""
流量检测模块 - B成员负责
Traffic Detection Module

主要功能：
1. 流量特征提取
2. 动态阈值检测
3. 统计分析算法实现
"""

__version__ = "1.0.0"
__author__ = "Team Member B"

# 导入主要模块
from .traffic_collector import TrafficCollector
from .feature_extractor import FeatureExtractor
from .dynamic_threshold import DynamicThreshold, MultiFeatureThreshold, HierarchicalThreshold, AdvancedAnomalyDetector
from .statistical_analyzer import StatisticalAnalyzer

__all__ = [
    'TrafficCollector',
    'FeatureExtractor',
    'DynamicThreshold',
    'MultiFeatureThreshold',
    'HierarchicalThreshold',
    'AdvancedAnomalyDetector',
    'StatisticalAnalyzer'
]
