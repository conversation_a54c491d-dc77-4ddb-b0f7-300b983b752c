#!/usr/bin/env python3
"""
机器学习检测器 - C成员
实现轻量级ML检测器（KNN/SVM/随机森林）
"""

import time
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import joblib
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.svm import SVC, OneClassSVM
from sklearn.neighbors import KNeighborsClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                           f1_score, confusion_matrix, classification_report)
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DetectionMode(Enum):
    """检测模式"""
    BINARY = "binary"           # 二分类（正常/异常）
    MULTICLASS = "multiclass"   # 多分类（正常/DDoS/扫描/泄露）
    ANOMALY = "anomaly"         # 异常检测

class ModelStatus(Enum):
    """模型状态"""
    UNTRAINED = "untrained"
    TRAINING = "training"
    TRAINED = "trained"
    PREDICTING = "predicting"
    ERROR = "error"

@dataclass
class PredictionResult:
    """预测结果"""
    flow_id: str
    prediction: int
    probability: float
    confidence: float
    attack_type: str
    threat_level: float
    timestamp: float

class MLDetector:
    """机器学习检测器"""
    
    def __init__(self, detector_id: str = "ml_detector"):
        """初始化ML检测器"""
        self.detector_id = detector_id
        self.detection_mode = DetectionMode.MULTICLASS
        self.model_status = ModelStatus.UNTRAINED
        
        # 模型集合
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            'svm': SVC(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                probability=True,
                random_state=42
            ),
            'knn': KNeighborsClassifier(
                n_neighbors=7,
                weights='distance',
                metric='minkowski'
            ),
            'decision_tree': DecisionTreeClassifier(
                max_depth=12,
                min_samples_split=5,
                random_state=42
            ),
            'naive_bayes': GaussianNB(),
            'logistic_regression': LogisticRegression(
                random_state=42,
                max_iter=1000
            )
        }
        
        # 异常检测模型
        self.anomaly_models = {
            'isolation_forest': IsolationForest(
                contamination=0.1,
                random_state=42
            ),
            'one_class_svm': OneClassSVM(
                kernel='rbf',
                gamma='scale',
                nu=0.1
            )
        }
        
        # 数据预处理
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        # 训练数据和模型
        self.training_data = None
        self.trained_models = {}
        self.model_performance = {}
        self.feature_importance = {}
        
        # 预测历史
        self.prediction_history = []
        self.max_history = 10000
        
        # 攻击类型映射
        self.attack_types = {
            0: 'normal',
            1: 'ddos',
            2: 'port_scan',
            3: 'data_exfiltration'
        }
        
        # 统计信息
        self.stats = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'false_positives': 0,
            'false_negatives': 0,
            'avg_prediction_time': 0.0,
            'model_accuracy': 0.0,
            'last_training_time': 0.0
        }
        
        logger.info(f"ML检测器 {detector_id} 初始化完成")
    
    def load_training_data(self, features: np.ndarray, labels: np.ndarray, 
                          feature_names: List[str] = None) -> bool:
        """加载训练数据"""
        try:
            if features.shape[0] != labels.shape[0]:
                raise ValueError("特征和标签数量不匹配")
            
            # 数据预处理
            features_scaled = self.scaler.fit_transform(features)
            labels_encoded = self.label_encoder.fit_transform(labels)
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                features_scaled, labels_encoded, 
                test_size=0.2, 
                random_state=42,
                stratify=labels_encoded
            )
            
            self.training_data = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test,
                'feature_names': feature_names or [f'feature_{i}' for i in range(features.shape[1])],
                'n_features': features.shape[1],
                'n_samples': features.shape[0],
                'n_classes': len(np.unique(labels_encoded))
            }
            
            logger.info(f"训练数据加载完成: {X_train.shape[0]} 训练样本, {X_test.shape[0]} 测试样本")
            logger.info(f"特征数量: {features.shape[1]}, 类别数量: {len(np.unique(labels_encoded))}")
            
            return True
            
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            return False
    
    def train_models(self, models_to_train: List[str] = None) -> Dict[str, float]:
        """训练模型"""
        if self.training_data is None:
            logger.error("未加载训练数据")
            return {}
        
        self.model_status = ModelStatus.TRAINING
        start_time = time.time()
        
        if models_to_train is None:
            models_to_train = list(self.models.keys())
        
        training_results = {}
        
        try:
            X_train = self.training_data['X_train']
            X_test = self.training_data['X_test']
            y_train = self.training_data['y_train']
            y_test = self.training_data['y_test']
            
            for model_name in models_to_train:
                if model_name not in self.models:
                    logger.warning(f"未知模型: {model_name}")
                    continue
                
                logger.info(f"训练模型: {model_name}")
                model_start_time = time.time()
                
                # 训练模型
                model = self.models[model_name]
                model.fit(X_train, y_train)
                
                # 评估模型
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
                recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
                f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                # 保存训练好的模型
                self.trained_models[model_name] = model
                
                # 保存性能指标
                performance = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'training_time': time.time() - model_start_time
                }
                
                self.model_performance[model_name] = performance
                training_results[model_name] = accuracy
                
                # 特征重要性（如果支持）
                if hasattr(model, 'feature_importances_'):
                    self.feature_importance[model_name] = model.feature_importances_
                elif hasattr(model, 'coef_'):
                    self.feature_importance[model_name] = np.abs(model.coef_[0])
                
                logger.info(f"模型 {model_name} 训练完成: 准确率={accuracy:.4f}, 用时={performance['training_time']:.2f}s")
            
            # 训练异常检测模型
            self._train_anomaly_models(X_train)
            
            # 选择最佳模型
            self.best_model_name = max(training_results, key=training_results.get)
            self.best_model = self.trained_models[self.best_model_name]
            
            self.model_status = ModelStatus.TRAINED
            self.stats['last_training_time'] = time.time() - start_time
            self.stats['model_accuracy'] = training_results[self.best_model_name]
            
            logger.info(f"模型训练完成，最佳模型: {self.best_model_name} (准确率: {self.stats['model_accuracy']:.4f})")
            
            return training_results
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            self.model_status = ModelStatus.ERROR
            return {}
    
    def _train_anomaly_models(self, X_train: np.ndarray):
        """训练异常检测模型"""
        try:
            # 只使用正常样本训练异常检测模型
            normal_samples = X_train[self.training_data['y_train'] == 0]
            
            for model_name, model in self.anomaly_models.items():
                logger.info(f"训练异常检测模型: {model_name}")
                model.fit(normal_samples)
                self.trained_models[f"anomaly_{model_name}"] = model
                
        except Exception as e:
            logger.error(f"训练异常检测模型失败: {e}")
    
    def predict(self, features: np.ndarray, flow_ids: List[str] = None) -> List[PredictionResult]:
        """进行预测"""
        if self.model_status != ModelStatus.TRAINED:
            logger.error("模型未训练")
            return []
        
        self.model_status = ModelStatus.PREDICTING
        start_time = time.time()
        
        try:
            # 预处理特征
            features_scaled = self.scaler.transform(features)
            
            # 生成流ID
            if flow_ids is None:
                flow_ids = [f"flow_{i}_{int(time.time())}" for i in range(len(features))]
            
            results = []
            
            for i, (feature_vector, flow_id) in enumerate(zip(features_scaled, flow_ids)):
                feature_vector = feature_vector.reshape(1, -1)
                
                # 使用最佳模型预测
                prediction = self.best_model.predict(feature_vector)[0]
                probabilities = self.best_model.predict_proba(feature_vector)[0]
                
                # 计算置信度
                confidence = np.max(probabilities)
                probability = probabilities[prediction]
                
                # 确定攻击类型
                attack_type = self.attack_types.get(prediction, 'unknown')
                
                # 计算威胁等级
                threat_level = self._calculate_threat_level(prediction, confidence, probabilities)
                
                # 创建预测结果
                result = PredictionResult(
                    flow_id=flow_id,
                    prediction=prediction,
                    probability=probability,
                    confidence=confidence,
                    attack_type=attack_type,
                    threat_level=threat_level,
                    timestamp=time.time()
                )
                
                results.append(result)
                
                # 保存预测历史
                self.prediction_history.append(result)
                if len(self.prediction_history) > self.max_history:
                    self.prediction_history.pop(0)
            
            # 更新统计信息
            prediction_time = time.time() - start_time
            self.stats['total_predictions'] += len(results)
            self.stats['avg_prediction_time'] = (
                (self.stats['avg_prediction_time'] * (self.stats['total_predictions'] - len(results)) + 
                 prediction_time) / self.stats['total_predictions']
            )
            
            self.model_status = ModelStatus.TRAINED
            
            logger.info(f"预测完成: {len(results)} 个样本, 用时: {prediction_time:.4f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            self.model_status = ModelStatus.ERROR
            return []
    
    def predict_single(self, features: np.ndarray, flow_id: str = None) -> Optional[PredictionResult]:
        """单个样本预测"""
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        results = self.predict(features, [flow_id or f"single_{int(time.time())}"])
        return results[0] if results else None
    
    def _calculate_threat_level(self, prediction: int, confidence: float, 
                              probabilities: np.ndarray) -> float:
        """计算威胁等级"""
        if prediction == 0:  # 正常流量
            return 0.0
        
        # 基础威胁等级
        base_threat = {
            1: 0.8,  # DDoS
            2: 0.6,  # 端口扫描
            3: 0.9   # 数据泄露
        }.get(prediction, 0.5)
        
        # 根据置信度调整
        threat_level = base_threat * confidence
        
        # 考虑概率分布的熵
        entropy = -np.sum(probabilities * np.log(probabilities + 1e-10))
        uncertainty_factor = 1 - (entropy / np.log(len(probabilities)))
        
        return min(1.0, threat_level * uncertainty_factor)
    
    def evaluate_model(self, model_name: str = None) -> Dict[str, Any]:
        """评估模型性能"""
        if self.training_data is None or not self.trained_models:
            logger.error("模型未训练或无测试数据")
            return {}
        
        if model_name is None:
            model_name = self.best_model_name
        
        if model_name not in self.trained_models:
            logger.error(f"模型 {model_name} 不存在")
            return {}
        
        try:
            model = self.trained_models[model_name]
            X_test = self.training_data['X_test']
            y_test = self.training_data['y_test']
            
            # 预测
            y_pred = model.predict(X_test)
            y_proba = model.predict_proba(X_test) if hasattr(model, 'predict_proba') else None
            
            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            # 混淆矩阵
            cm = confusion_matrix(y_test, y_pred)
            
            # 分类报告
            class_report = classification_report(y_test, y_pred, output_dict=True, zero_division=0)
            
            evaluation_result = {
                'model_name': model_name,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'confusion_matrix': cm.tolist(),
                'classification_report': class_report,
                'test_samples': len(y_test),
                'feature_importance': self.feature_importance.get(model_name, []).tolist() if model_name in self.feature_importance else []
            }
            
            logger.info(f"模型 {model_name} 评估完成: 准确率={accuracy:.4f}")
            
            return evaluation_result
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return {}
    
    def get_predictions(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取预测结果"""
        recent_predictions = self.prediction_history[-limit:]
        
        return [
            {
                'flow_id': pred.flow_id,
                'prediction': pred.prediction,
                'attack_type': pred.attack_type,
                'threat_level': pred.threat_level,
                'confidence': pred.confidence,
                'timestamp': pred.timestamp
            }
            for pred in recent_predictions
        ]
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        return {
            'detector_id': self.detector_id,
            'model_status': self.model_status.value,
            'detection_mode': self.detection_mode.value,
            'trained_models': list(self.trained_models.keys()),
            'best_model': getattr(self, 'best_model_name', None),
            'model_performance': self.model_performance,
            'stats': self.stats.copy(),
            'prediction_history_size': len(self.prediction_history)
        }
    
    def save_model(self, filepath: str, model_name: str = None) -> bool:
        """保存模型"""
        try:
            if model_name is None:
                model_name = self.best_model_name
            
            if model_name not in self.trained_models:
                logger.error(f"模型 {model_name} 不存在")
                return False
            
            model_data = {
                'model': self.trained_models[model_name],
                'scaler': self.scaler,
                'label_encoder': self.label_encoder,
                'model_performance': self.model_performance.get(model_name, {}),
                'feature_importance': self.feature_importance.get(model_name, []),
                'attack_types': self.attack_types,
                'stats': self.stats
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"模型 {model_name} 已保存到: {filepath}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            model_data = joblib.load(filepath)
            
            # 恢复模型和预处理器
            model_name = "loaded_model"
            self.trained_models[model_name] = model_data['model']
            self.best_model_name = model_name
            self.best_model = model_data['model']
            
            self.scaler = model_data['scaler']
            self.label_encoder = model_data['label_encoder']
            self.attack_types = model_data.get('attack_types', self.attack_types)
            
            # 恢复性能信息
            self.model_performance[model_name] = model_data.get('model_performance', {})
            self.feature_importance[model_name] = model_data.get('feature_importance', [])
            self.stats.update(model_data.get('stats', {}))
            
            self.model_status = ModelStatus.TRAINED
            
            logger.info(f"模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return False
    
    def is_healthy(self) -> bool:
        """检查检测器健康状态"""
        return (self.model_status in [ModelStatus.TRAINED, ModelStatus.PREDICTING] and
                len(self.trained_models) > 0 and
                hasattr(self, 'best_model'))

if __name__ == "__main__":
    # 测试ML检测器
    detector = MLDetector("test_detector")
    
    # 生成模拟数据
    np.random.seed(42)
    n_samples = 1000
    n_features = 23
    
    # 生成特征
    X = np.random.random((n_samples, n_features))
    
    # 生成标签（模拟不同攻击类型）
    y = np.random.choice([0, 1, 2, 3], n_samples, p=[0.7, 0.1, 0.1, 0.1])
    
    # 训练模型
    detector.load_training_data(X, y)
    training_results = detector.train_models(['random_forest', 'svm', 'knn'])
    
    print(f"训练结果: {training_results}")
    
    # 评估模型
    evaluation = detector.evaluate_model()
    print(f"模型评估: {json.dumps(evaluation, indent=2, default=str)}")
    
    # 预测测试
    test_X = np.random.random((10, n_features))
    predictions = detector.predict(test_X)
    
    for pred in predictions:
        print(f"预测: {pred.attack_type}, 威胁等级: {pred.threat_level:.3f}, 置信度: {pred.confidence:.3f}")
    
    # 状态检查
    status = detector.get_model_status()
    print(f"检测器状态: {json.dumps(status, indent=2, default=str)}")
