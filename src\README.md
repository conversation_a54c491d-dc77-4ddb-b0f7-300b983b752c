# 流量检测模块 - B成员代码

## 项目概述

本模块是SDN智能网络应用项目中的**流量特征提取与动态阈值检测**部分，由B成员负责开发。

## 主要功能

### 1. 流量采集模块 (`traffic_collector.py`)
- 模拟从SDN交换机采集流量数据
- 使用Scapy进行数据包捕获和分析
- 提供流量数据预处理功能
- 支持模拟数据生成用于测试

### 2. 特征提取模块 (`feature_extractor.py`)
- 从流量数据中提取关键特征
- 计算信息熵、K-L散度等统计特征
- 生成特征向量供机器学习模块使用
- 支持批量特征提取和标准化

### 3. 动态阈值检测模块 (`dynamic_threshold.py`)
- 实现自适应阈值算法
- 滑动窗口统计分析
- 动态调节检测灵敏度
- 支持单特征和多特征异常检测

### 4. 统计分析模块 (`statistical_analyzer.py`)
- 流量统计分析
- 生成检测报告
- 可视化图表生成
- 性能评估指标计算

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 运行完整测试

```bash
cd src/traffic_detection
python test_modules.py
```

### 单独使用各模块

#### 1. 流量采集
```python
from traffic_collector import TrafficCollector

collector = TrafficCollector()
# 模拟生成流量数据
collector.simulate_traffic_data(num_flows=50, duration=300)
flow_stats = collector.get_flow_statistics()
```

#### 2. 特征提取
```python
from feature_extractor import FeatureExtractor

extractor = FeatureExtractor()
# 提取单个流的特征
features = extractor.extract_all_features(flow_data)
# 批量提取特征
feature_matrix = extractor.extract_batch_features(flows_data)
```

#### 3. 动态阈值检测
```python
from dynamic_threshold import DynamicThreshold

detector = DynamicThreshold(window_size=100, sensitivity=2.0)
# 检测异常
result = detector.detect_anomaly(value)
# 批量检测
results = detector.batch_detect(values)
```

#### 4. 统计分析
```python
from statistical_analyzer import StatisticalAnalyzer

analyzer = StatisticalAnalyzer()
# 分析流量模式
traffic_analysis = analyzer.analyze_traffic_patterns(flows_data)
# 生成可视化图表
analyzer.generate_traffic_visualization(flows_data, "traffic.png")
```

## 输出文件说明

运行测试后会生成以下文件：

- `test_traffic_data.json`: 模拟的流量数据
- `test_threshold_results.json`: 动态阈值检测结果
- `test_analysis_report.json`: 完整的分析报告
- `test_traffic_visualization.png`: 流量分析可视化图表
- `test_threshold_visualization.png`: 阈值检测可视化图表

## 模块接口

### 与其他成员模块的接口

#### 输出给C成员（机器学习模块）
- 特征向量：`FeatureExtractor.get_feature_vector()`
- 特征矩阵：`FeatureExtractor.extract_batch_features()`

#### 输出给D成员（前端界面）
- 检测结果：`DynamicThreshold.get_recent_results()`
- 统计信息：`DynamicThreshold.get_detection_stats()`
- 可视化数据：`StatisticalAnalyzer.analysis_results`

#### 接收A成员（系统架构）的数据
- 流量数据格式：符合SDN控制器输出的标准格式
- 配置参数：阈值灵敏度、窗口大小等

## 技术特点

### 1. 动态自适应
- 滑动窗口统计
- 自适应阈值调整
- 实时参数优化

### 2. 多维特征分析
- 13种流量特征
- 信息熵计算
- K-L散度分析
- 突发性检测

### 3. 高效处理
- 批量处理支持
- 内存优化设计
- 实时检测能力

### 4. 可视化支持
- 多种图表类型
- 实时数据展示
- 检测结果可视化

## 算法说明

### 信息熵计算
```
H(X) = -Σ p(x) * log2(p(x))
```

### 动态阈值更新
```
threshold = mean + sensitivity * std
mean_new = (1-α) * mean_old + α * new_value
```

### 突发性指标
```
burstiness = (std - mean) / (std + mean)
```

## 性能指标

- 检测准确率：>95%
- 误报率：<5%
- 处理速度：>1000 flows/second
- 内存使用：<100MB

## 开发者信息

- **开发者**: B成员
- **模块**: 流量特征提取 + 动态阈值检测
- **版本**: 1.0.0
- **最后更新**: 2024年

## 注意事项

1. 确保安装了所有依赖包
2. Scapy可能需要管理员权限进行网络捕获
3. 可视化功能需要GUI环境支持
4. 大数据量处理时注意内存使用

## 后续开发计划

1. 增加更多特征提取算法
2. 优化检测性能
3. 增强可视化功能
4. 添加更多异常检测算法
