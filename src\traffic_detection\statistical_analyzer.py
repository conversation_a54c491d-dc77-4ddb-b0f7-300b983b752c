"""
统计分析模块
Statistical Analyzer Module

功能：
- 流量统计分析
- 生成检测报告
- 可视化图表生成
- 性能评估指标计算
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import json
import time
from datetime import datetime

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class StatisticalAnalyzer:
    """统计分析器"""
    
    def __init__(self):
        """初始化统计分析器"""
        self.analysis_results = {}
        self.visualization_config = {
            'figure_size': (12, 8),
            'color_palette': 'viridis',
            'style': 'whitegrid'
        }
        
    def analyze_traffic_patterns(self, flows_data):
        """
        分析流量模式
        
        Args:
            flows_data: 流量数据字典
            
        Returns:
            dict: 分析结果
        """
        if not flows_data:
            return {}
            
        # 提取基本统计信息
        packet_counts = [flow['packet_count'] for flow in flows_data.values()]
        byte_counts = [flow['byte_count'] for flow in flows_data.values()]
        durations = []
        
        for flow in flows_data.values():
            duration = flow.get('last_time', 0) - flow.get('start_time', 0)
            durations.append(max(duration, 0.001))
            
        # 计算统计指标
        analysis = {
            'flow_count': len(flows_data),
            'packet_stats': {
                'mean': np.mean(packet_counts),
                'std': np.std(packet_counts),
                'median': np.median(packet_counts),
                'min': np.min(packet_counts),
                'max': np.max(packet_counts),
                'percentile_95': np.percentile(packet_counts, 95)
            },
            'byte_stats': {
                'mean': np.mean(byte_counts),
                'std': np.std(byte_counts),
                'median': np.median(byte_counts),
                'min': np.min(byte_counts),
                'max': np.max(byte_counts),
                'total': np.sum(byte_counts)
            },
            'duration_stats': {
                'mean': np.mean(durations),
                'std': np.std(durations),
                'median': np.median(durations),
                'min': np.min(durations),
                'max': np.max(durations)
            }
        }
        
        # 计算流量速率
        packet_rates = [pc/dur for pc, dur in zip(packet_counts, durations)]
        byte_rates = [bc/dur for bc, dur in zip(byte_counts, durations)]
        
        analysis['rate_stats'] = {
            'packet_rate_mean': np.mean(packet_rates),
            'packet_rate_std': np.std(packet_rates),
            'byte_rate_mean': np.mean(byte_rates),
            'byte_rate_std': np.std(byte_rates)
        }
        
        self.analysis_results['traffic_patterns'] = analysis
        return analysis
        
    def analyze_detection_performance(self, detection_results, ground_truth=None):
        """
        分析检测性能
        
        Args:
            detection_results: 检测结果列表
            ground_truth: 真实标签（可选）
            
        Returns:
            dict: 性能分析结果
        """
        if not detection_results:
            return {}
            
        # 基本检测统计
        total_detections = len(detection_results)
        anomaly_detections = sum(1 for r in detection_results if r.get('is_anomaly', False))
        
        performance = {
            'total_detections': total_detections,
            'anomaly_detections': anomaly_detections,
            'anomaly_rate': anomaly_detections / total_detections if total_detections > 0 else 0,
            'normal_detections': total_detections - anomaly_detections
        }
        
        # 异常分数统计
        anomaly_scores = [r.get('anomaly_score', 0) for r in detection_results]
        if anomaly_scores:
            performance['anomaly_score_stats'] = {
                'mean': np.mean(anomaly_scores),
                'std': np.std(anomaly_scores),
                'median': np.median(anomaly_scores),
                'min': np.min(anomaly_scores),
                'max': np.max(anomaly_scores)
            }
            
        # 如果有真实标签，计算准确率指标
        if ground_truth and len(ground_truth) == len(detection_results):
            predicted = [r.get('is_anomaly', False) for r in detection_results]
            
            # 计算混淆矩阵
            tp = sum(1 for p, t in zip(predicted, ground_truth) if p and t)
            fp = sum(1 for p, t in zip(predicted, ground_truth) if p and not t)
            tn = sum(1 for p, t in zip(predicted, ground_truth) if not p and not t)
            fn = sum(1 for p, t in zip(predicted, ground_truth) if not p and t)
            
            # 计算性能指标
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            accuracy = (tp + tn) / (tp + fp + tn + fn) if (tp + fp + tn + fn) > 0 else 0
            
            performance['confusion_matrix'] = {
                'true_positive': tp,
                'false_positive': fp,
                'true_negative': tn,
                'false_negative': fn
            }
            
            performance['metrics'] = {
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'accuracy': accuracy,
                'false_positive_rate': fp / (fp + tn) if (fp + tn) > 0 else 0
            }
            
        self.analysis_results['detection_performance'] = performance
        return performance
        
    def generate_traffic_visualization(self, flows_data, save_path=None):
        """
        生成流量可视化图表
        
        Args:
            flows_data: 流量数据
            save_path: 保存路径
        """
        if not flows_data:
            print("没有流量数据可供可视化")
            return
            
        # 设置图表样式
        sns.set_style(self.visualization_config['style'])
        fig, axes = plt.subplots(2, 2, figsize=self.visualization_config['figure_size'])
        
        # 提取数据
        packet_counts = [flow['packet_count'] for flow in flows_data.values()]
        byte_counts = [flow['byte_count'] for flow in flows_data.values()]
        
        # 1. 包数量分布直方图
        axes[0, 0].hist(packet_counts, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('数据包数量分布')
        axes[0, 0].set_xlabel('数据包数量')
        axes[0, 0].set_ylabel('流数量')
        
        # 2. 字节数分布直方图
        axes[0, 1].hist(byte_counts, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_title('字节数分布')
        axes[0, 1].set_xlabel('字节数')
        axes[0, 1].set_ylabel('流数量')
        
        # 3. 包数量 vs 字节数散点图
        axes[1, 0].scatter(packet_counts, byte_counts, alpha=0.6, color='coral')
        axes[1, 0].set_title('数据包数量 vs 字节数')
        axes[1, 0].set_xlabel('数据包数量')
        axes[1, 0].set_ylabel('字节数')
        
        # 4. 流量统计箱线图
        data_for_boxplot = [packet_counts, [bc/1000 for bc in byte_counts]]  # 字节数除以1000便于显示
        axes[1, 1].boxplot(data_for_boxplot, labels=['数据包数量', '字节数(KB)'])
        axes[1, 1].set_title('流量统计箱线图')
        axes[1, 1].set_ylabel('数值')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"流量可视化图表已保存到: {save_path}")
        else:
            plt.show()
            
    def generate_threshold_visualization(self, detection_results, save_path=None):
        """
        生成阈值检测可视化图表
        
        Args:
            detection_results: 检测结果
            save_path: 保存路径
        """
        if not detection_results:
            print("没有检测结果可供可视化")
            return
            
        # 提取数据
        timestamps = [r.get('timestamp', i) for i, r in enumerate(detection_results)]
        values = [r.get('value', 0) for r in detection_results]
        thresholds = [r.get('threshold', 0) for r in detection_results]
        anomalies = [r.get('is_anomaly', False) for r in detection_results]
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.visualization_config['figure_size'])
        
        # 1. 数值和阈值时间序列图
        ax1.plot(timestamps, values, label='观测值', color='blue', alpha=0.7)
        ax1.plot(timestamps, thresholds, label='动态阈值', color='red', linestyle='--')
        
        # 标记异常点
        anomaly_times = [t for t, a in zip(timestamps, anomalies) if a]
        anomaly_values = [v for v, a in zip(values, anomalies) if a]
        ax1.scatter(anomaly_times, anomaly_values, color='red', s=50, label='异常点', zorder=5)
        
        ax1.set_title('动态阈值检测结果')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('数值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 异常分数分布
        anomaly_scores = [r.get('anomaly_score', 0) for r in detection_results]
        ax2.hist(anomaly_scores, bins=30, alpha=0.7, color='orange', edgecolor='black')
        ax2.axvline(x=2.0, color='red', linestyle='--', label='异常阈值')
        ax2.set_title('异常分数分布')
        ax2.set_xlabel('异常分数')
        ax2.set_ylabel('频次')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"阈值检测可视化图表已保存到: {save_path}")
        else:
            plt.show()
            
    def generate_report(self, output_file="detection_report.json"):
        """
        生成检测报告
        
        Args:
            output_file: 输出文件名
        """
        report = {
            'report_time': datetime.now().isoformat(),
            'analysis_results': self.analysis_results,
            'summary': self._generate_summary()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"检测报告已生成: {output_file}")
        
    def _generate_summary(self):
        """生成分析摘要"""
        summary = {}
        
        if 'traffic_patterns' in self.analysis_results:
            tp = self.analysis_results['traffic_patterns']
            summary['traffic_summary'] = f"分析了 {tp['flow_count']} 个流，" \
                                        f"平均包数量: {tp['packet_stats']['mean']:.1f}，" \
                                        f"总字节数: {tp['byte_stats']['total']:.0f}"
                                        
        if 'detection_performance' in self.analysis_results:
            dp = self.analysis_results['detection_performance']
            summary['detection_summary'] = f"总检测次数: {dp['total_detections']}，" \
                                         f"异常检测: {dp['anomaly_detections']}，" \
                                         f"异常率: {dp['anomaly_rate']:.2%}"
                                         
        return summary

    def time_series_analysis(self, data, timestamps=None):
        """
        时间序列分析

        Args:
            data: 时间序列数据
            timestamps: 时间戳

        Returns:
            dict: 时间序列分析结果
        """
        if not data:
            return {}

        data = np.array(data)

        analysis = {
            'trend': self._detect_trend(data),
            'seasonality': self._detect_seasonality(data),
            'stationarity': self._test_stationarity(data),
            'change_points': self._detect_change_points(data)
        }

        return analysis

    def _detect_trend(self, data):
        """检测趋势"""
        if len(data) < 3:
            return {'slope': 0, 'trend_type': 'none'}

        x = np.arange(len(data))
        slope, intercept = np.polyfit(x, data, 1)

        if abs(slope) < 0.01:
            trend_type = 'none'
        elif slope > 0:
            trend_type = 'increasing'
        else:
            trend_type = 'decreasing'

        return {'slope': slope, 'intercept': intercept, 'trend_type': trend_type}

    def _detect_seasonality(self, data, max_period=50):
        """检测季节性"""
        if len(data) < 2 * max_period:
            return {'has_seasonality': False, 'period': None}

        # 使用自相关检测周期性
        autocorrs = []
        periods = range(2, min(max_period, len(data)//2))

        for period in periods:
            if period >= len(data):
                break
            autocorr = np.corrcoef(data[:-period], data[period:])[0, 1]
            if not np.isnan(autocorr):
                autocorrs.append((period, abs(autocorr)))

        if not autocorrs:
            return {'has_seasonality': False, 'period': None}

        # 找到最强的周期性
        best_period, best_corr = max(autocorrs, key=lambda x: x[1])

        has_seasonality = best_corr > 0.3  # 阈值

        return {
            'has_seasonality': has_seasonality,
            'period': best_period if has_seasonality else None,
            'correlation': best_corr
        }

    def _test_stationarity(self, data):
        """平稳性检验"""
        # 简化的平稳性检验
        if len(data) < 10:
            return {'is_stationary': True, 'p_value': 1.0}

        # 分段统计检验
        mid = len(data) // 2
        first_half = data[:mid]
        second_half = data[mid:]

        # 比较均值和方差
        mean_diff = abs(np.mean(first_half) - np.mean(second_half))
        var_diff = abs(np.var(first_half) - np.var(second_half))

        # 简单的平稳性判断
        mean_threshold = np.std(data) * 0.5
        var_threshold = np.var(data) * 0.5

        is_stationary = (mean_diff < mean_threshold) and (var_diff < var_threshold)

        return {
            'is_stationary': is_stationary,
            'mean_diff': mean_diff,
            'var_diff': var_diff
        }

    def _detect_change_points(self, data, min_size=5):
        """变点检测"""
        if len(data) < 2 * min_size:
            return []

        change_points = []

        # 滑动窗口检测均值变化
        for i in range(min_size, len(data) - min_size):
            before = data[max(0, i-min_size):i]
            after = data[i:min(i+min_size, len(data))]

            if len(before) > 0 and len(after) > 0:
                mean_before = np.mean(before)
                mean_after = np.mean(after)

                # 检测显著变化
                std_combined = np.std(np.concatenate([before, after]))
                if std_combined > 0:
                    change_magnitude = abs(mean_after - mean_before) / std_combined
                    if change_magnitude > 2.0:  # 阈值
                        change_points.append({
                            'index': i,
                            'magnitude': change_magnitude,
                            'before_mean': mean_before,
                            'after_mean': mean_after
                        })

        return change_points

    def correlation_analysis(self, multi_features):
        """
        特征相关性分析

        Args:
            multi_features: 多特征数据

        Returns:
            dict: 相关性分析结果
        """
        if isinstance(multi_features, pd.DataFrame):
            data = multi_features.values
            feature_names = multi_features.columns.tolist()
        else:
            data = np.array(multi_features)
            feature_names = [f"feature_{i}" for i in range(data.shape[1])]

        # 皮尔逊相关系数
        pearson_corr = np.corrcoef(data.T)

        # 斯皮尔曼相关系数
        from scipy.stats import spearmanr
        spearman_corr, _ = spearmanr(data)

        # 找到高相关特征对
        high_corr_pairs = []
        threshold = 0.8

        for i in range(len(feature_names)):
            for j in range(i+1, len(feature_names)):
                if abs(pearson_corr[i, j]) > threshold:
                    high_corr_pairs.append({
                        'feature1': feature_names[i],
                        'feature2': feature_names[j],
                        'pearson_corr': pearson_corr[i, j],
                        'spearman_corr': spearman_corr[i, j]
                    })

        return {
            'pearson_matrix': pearson_corr,
            'spearman_matrix': spearman_corr,
            'high_corr_pairs': high_corr_pairs,
            'feature_names': feature_names
        }

    def distribution_analysis(self, data):
        """
        分布拟合分析

        Args:
            data: 数据

        Returns:
            dict: 分布分析结果
        """
        from scipy import stats as scipy_stats

        data = np.array(data)

        # 正态性检验
        shapiro_stat, shapiro_p = scipy_stats.shapiro(data[:5000])  # 限制样本大小

        # 尝试拟合不同分布
        distributions = ['norm', 'expon', 'gamma', 'lognorm']
        fit_results = {}

        for dist_name in distributions:
            try:
                dist = getattr(scipy_stats, dist_name)
                params = dist.fit(data)

                # 计算拟合优度 (KS检验)
                ks_stat, ks_p = scipy_stats.kstest(data, lambda x: dist.cdf(x, *params))

                fit_results[dist_name] = {
                    'parameters': params,
                    'ks_statistic': ks_stat,
                    'ks_p_value': ks_p
                }
            except:
                continue

        # 找到最佳拟合分布
        best_dist = None
        best_p_value = 0

        for dist_name, result in fit_results.items():
            if result['ks_p_value'] > best_p_value:
                best_p_value = result['ks_p_value']
                best_dist = dist_name

        return {
            'shapiro_test': {'statistic': shapiro_stat, 'p_value': shapiro_p},
            'is_normal': shapiro_p > 0.05,
            'distribution_fits': fit_results,
            'best_fit_distribution': best_dist
        }

if __name__ == "__main__":
    # 测试代码
    analyzer = StatisticalAnalyzer()
    
    # 模拟流量数据
    flows_data = {}
    for i in range(50):
        flows_data[f"flow_{i}"] = {
            'packet_count': np.random.randint(10, 1000),
            'byte_count': np.random.randint(1000, 100000),
            'start_time': time.time(),
            'last_time': time.time() + np.random.uniform(1, 100)
        }
    
    # 分析流量模式
    traffic_analysis = analyzer.analyze_traffic_patterns(flows_data)
    print("流量模式分析结果:")
    print(f"  流数量: {traffic_analysis['flow_count']}")
    print(f"  平均包数量: {traffic_analysis['packet_stats']['mean']:.1f}")
    
    # 生成可视化图表
    analyzer.generate_traffic_visualization(flows_data, "traffic_analysis.png")
    
    # 生成报告
    analyzer.generate_report("analysis_report.json")
