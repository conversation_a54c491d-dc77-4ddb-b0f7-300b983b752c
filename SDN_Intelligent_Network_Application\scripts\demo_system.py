#!/usr/bin/env python3
"""
SDN智能网络应用演示脚本
展示B成员和D成员整合后的完整功能
"""

import os
import sys
import time
import threading
import subprocess
import webbrowser
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / 'src'))

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🌐 SDN智能网络应用 - 完整演示系统                          ║
║                                                                              ║
║  团队协作项目展示：                                                           ║
║  🏗️  A成员: 系统架构设计                                                      ║
║  🔍 B成员: 流量检测与特征提取 (已整合)                                        ║
║  🤖 C成员: 机器学习异常检测                                                   ║
║  🎨 D成员: 前端界面与可视化 (已整合)                                          ║
║  🎭 E成员: 系统演示与展示                                                     ║
║                                                                              ║
║  本演示展示B成员和D成员的完整整合功能                                         ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def start_backend_server():
    """启动Flask后端服务器"""
    print("🚀 启动Flask后端服务器...")
    
    backend_script = project_root / 'src/member_d_frontend/backend/app.py'
    
    # 启动后端进程
    process = subprocess.Popen([
        sys.executable, str(backend_script)
    ], cwd=str(project_root))
    
    return process

def wait_for_server(url="http://localhost:5000", timeout=30):
    """等待服务器启动"""
    import requests
    
    print(f"⏳ 等待服务器启动 ({url})...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print("✅ 服务器启动成功！")
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(1)
    
    print("❌ 服务器启动超时")
    return False

def demonstrate_b_member_features():
    """演示B成员功能"""
    print("\n🔍 演示B成员流量检测功能...")
    
    try:
        from member_b_detection.enhanced_traffic_collector import EnhancedTrafficCollector
        from member_b_detection.feature_extractor import FeatureExtractor
        from member_b_detection.dynamic_threshold import DynamicThreshold
        from member_b_detection.statistical_analyzer import StatisticalAnalyzer
        
        # 创建实例
        collector = EnhancedTrafficCollector()
        extractor = FeatureExtractor()
        detector = DynamicThreshold()
        analyzer = StatisticalAnalyzer()
        
        print("✅ B成员模块初始化成功")
        
        # 启动流量采集
        collector.start_collection()
        print("📊 开始流量采集...")
        
        # 模拟不同类型的攻击
        attack_types = ['ddos', 'port_scan', 'data_exfiltration']
        for attack_type in attack_types:
            print(f"🎯 模拟 {attack_type} 攻击...")
            collector.simulate_attack_scenario(attack_type, duration=3)
            time.sleep(1)
        
        # 获取流量统计
        flow_stats = collector.get_flow_statistics()
        print(f"📈 生成了 {len(flow_stats)} 个流量记录")
        
        # 特征提取
        if flow_stats:
            feature_matrix = extractor.extract_batch_features(flow_stats)
            print(f"🔬 提取了 {feature_matrix.shape[1]} 个特征维度")
            
            # 异常检测
            packet_counts = feature_matrix['packet_count'].values
            results = detector.batch_detect(packet_counts)
            anomaly_count = sum(1 for r in results if r.get('is_anomaly', False))
            print(f"🚨 检测到 {anomaly_count} 个异常流量")
            
            # 统计分析
            traffic_analysis = analyzer.analyze_traffic_patterns(flow_stats)
            detection_performance = analyzer.analyze_detection_performance(results)
            
            print(f"📊 流量分析完成，异常率: {detection_performance.get('anomaly_rate', 0):.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ B成员功能演示失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔗 测试API端点...")
    
    import requests
    
    base_url = 'http://localhost:5000'
    endpoints = [
        ('/api/realtime-stats', 'GET'),
        ('/api/detection-results', 'GET'),
        ('/api/traffic-features', 'GET'),
        ('/api/performance-metrics', 'GET'),
        ('/api/topology', 'GET'),
        ('/api/flowtables', 'GET'),
        ('/api/generate-demo', 'POST'),
        ('/api/trigger-detection', 'POST')
    ]
    
    success_count = 0
    for endpoint, method in endpoints:
        try:
            if method == 'GET':
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {method} {endpoint}")
                success_count += 1
            else:
                print(f"⚠️ {method} {endpoint} (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {method} {endpoint} (错误: {e})")
    
    print(f"📊 API测试结果: {success_count}/{len(endpoints)} 个端点正常")
    return success_count >= len(endpoints) // 2

def open_web_interface():
    """打开Web界面"""
    print("\n🌐 打开Web界面...")
    
    url = 'http://localhost:5000'
    try:
        webbrowser.open(url)
        print(f"✅ 已在浏览器中打开: {url}")
        return True
    except Exception as e:
        print(f"❌ 无法打开浏览器: {e}")
        print(f"💡 请手动访问: {url}")
        return False

def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示模式")
    print("="*50)
    
    while True:
        print("\n请选择演示功能:")
        print("1. 🔍 演示B成员流量检测")
        print("2. 🔗 测试API端点")
        print("3. 🌐 打开Web界面")
        print("4. 📊 生成演示数据")
        print("5. 🚨 触发异常检测")
        print("0. 退出演示")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("👋 演示结束，感谢使用！")
            break
        elif choice == '1':
            demonstrate_b_member_features()
        elif choice == '2':
            test_api_endpoints()
        elif choice == '3':
            open_web_interface()
        elif choice == '4':
            generate_demo_data()
        elif choice == '5':
            trigger_detection()
        else:
            print("❌ 无效选择，请重试")

def generate_demo_data():
    """生成演示数据"""
    print("\n📊 生成演示数据...")
    
    import requests
    
    try:
        response = requests.post('http://localhost:5000/api/generate-demo', timeout=15)
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                print(f"✅ 演示数据生成成功!")
                print(f"   总流量: {result.get('total_flows', 0)}")
                print(f"   正常流量: {result.get('normal_flows', 0)}")
                print(f"   攻击流量: {result.get('attack_flows', 0)}")
            else:
                print(f"❌ 生成失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 生成演示数据失败: {e}")

def trigger_detection():
    """触发异常检测"""
    print("\n🚨 触发异常检测...")
    
    import requests
    
    try:
        response = requests.post('http://localhost:5000/api/trigger-detection', timeout=15)
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                print(f"✅ 检测完成!")
                print(f"   处理检测: {result.get('total_detections', 0)}")
                print(f"   发现异常: {result.get('anomaly_count', 0)}")
            else:
                print(f"❌ 检测失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 触发检测失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    print("🔧 准备演示环境...")
    
    # 检查依赖
    try:
        import requests
        import flask
        import flask_socketio
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请运行: pip install requests flask flask-socketio flask-cors")
        return
    
    # 启动后端服务器
    backend_process = start_backend_server()
    
    try:
        # 等待服务器启动
        if wait_for_server():
            print("🎉 系统启动成功！")
            
            # 演示B成员功能
            demonstrate_b_member_features()
            
            # 测试API
            test_api_endpoints()
            
            # 打开Web界面
            open_web_interface()
            
            # 进入交互模式
            interactive_demo()
        else:
            print("❌ 服务器启动失败")
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断演示")
    
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        backend_process.terminate()
        backend_process.wait()
        print("✅ 演示结束")

if __name__ == '__main__':
    main()
