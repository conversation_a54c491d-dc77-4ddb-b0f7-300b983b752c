# SPDX-License-Identifier: GPL-2.0-only
# This file is part of Scapy
# See https://scapy.net/ for more information
# Copyright (C) 2007, 2008, 2009 <PERSON><PERSON><PERSON> <<EMAIL>>
#               2015, 2016, 2017 <PERSON><PERSON> <<EMAIL>>
#               2019 <PERSON><PERSON>

"""
Tools for handling TLS sessions and digital certificates.
Use load_layer('tls') to load them to the main namespace.

Prerequisites:

    - You may need to 'pip install cryptography' for the module to be loaded.


Main features:

    - X.509 certificates parsing/building.

    - RSA & ECDSA keys sign/verify methods.

    - TLS records and sublayers (handshake...) parsing/building. Works with
      versions SSLv2 to TLS 1.3. This may be enhanced by a TLS context. For
      instance, if <PERSON>ap<PERSON> reads a ServerHello with version TLS 1.2 and a cipher
      suite using AES, it will assume the presence of IVs prepending the data.
      See test/tls.uts for real examples.

    - TLS encryption/decryption capabilities with many ciphersuites, including
      some which may be deemed dangerous. Once again, the TLS context enables
      <PERSON>apy to transparently send/receive protected data if it learnt the
      session secrets. Note that if <PERSON><PERSON><PERSON> acts as one side of the handshake
      (e.g. reads all server-related packets and builds all client-related
      packets), it will indeed compute the session secrets.

    - T<PERSON> client & server basic automatons, provided for testing and tweaking
      purposes. These make for a very primitive TLS stack.

    - Additionally, a basic test PKI (key + certificate for a CA, a client and
      a server) is provided in tls/examples/pki_test.


Unit tests:

    - Various cryptography checks.

    - Reading a TLS handshake between a Firefox client and a GitHub server.

    - Reading TLS 1.3 handshakes from test vectors of the 8448 RFC.

    - Reading a SSLv2 handshake between s_client and s_server, without PFS.

    - Test our TLS server against s_client with different cipher suites.

    - Test our TLS client against our TLS server (s_server is unscriptable).

    - Test our TLS client against python's SSL Socket wrapper (for TLS 1.3)


TODO list (may it be carved away by good souls):

    - Features to add (or wait for) in the cryptography library:

        - the compressed EC point format.

    - About the automatons:

        - Allow upgrade from TLS 1.2 to TLS 1.3 in the Automaton client.
          Currently we'll use TLS 1.3 only if the automaton client was given
          version="tls13".

        - Add various checks for discrepancies between client and server.
          Is the ServerHello ciphersuite ok? What about the SKE params? Etc.

        - Add some examples which illustrate how the automatons could be used.
          Typically, we could showcase this with Heartbleed.

        - Allow the server to store both one RSA key and one ECDSA key, and
          select the right one to use according to the ClientHello suites.


    - Miscellaneous:

        - Define several Certificate Transparency objects.

        - Mostly unused features : DSS, fixed DH, SRP, char2 curves...
"""

from scapy.config import conf

if not conf.crypto_valid:
    import logging
    log_loading = logging.getLogger("scapy.loading")
    log_loading.info("Can't import python-cryptography v1.7+. "
                     "Disabled PKI & TLS crypto-related features.")
