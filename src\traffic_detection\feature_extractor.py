"""
特征提取模块
Feature Extractor Module

功能：
- 从流量数据中提取关键特征
- 计算信息熵、K-L散度等统计特征
- 生成特征向量供机器学习模块使用
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.spatial.distance import jense<PERSON><PERSON>non
from collections import Counter
import math

class FeatureExtractor:
    """流量特征提取器"""
    
    def __init__(self):
        """初始化特征提取器"""
        self.feature_names = [
            'packet_count', 'byte_count', 'duration', 'avg_packet_size',
            'packet_size_std', 'avg_inter_arrival', 'inter_arrival_std',
            'packet_rate', 'byte_rate', 'entropy_packet_size',
            'entropy_inter_arrival', 'burstiness', 'flow_symmetry',
            'hurst_exponent', 'fractal_dimension', 'lyapunov_exponent',
            'conditional_entropy', 'is_tcp', 'is_udp', 'is_icmp',
            'tcp_window_variation', 'tcp_retransmission_rate', 'udp_burst_pattern'
        ]
        
    def extract_basic_features(self, flow_data):
        """
        提取基本流量特征
        
        Args:
            flow_data: 单个流的统计数据
            
        Returns:
            dict: 基本特征字典
        """
        features = {}
        
        # 基本统计特征
        features['packet_count'] = flow_data.get('packet_count', 0)
        features['byte_count'] = flow_data.get('byte_count', 0)
        
        # 时间特征
        start_time = flow_data.get('start_time', 0)
        last_time = flow_data.get('last_time', 0)
        features['duration'] = max(last_time - start_time, 0.001)  # 避免除零
        
        # 包大小特征
        packet_sizes = flow_data.get('packet_sizes', [])
        if packet_sizes:
            features['avg_packet_size'] = np.mean(packet_sizes)
            features['packet_size_std'] = np.std(packet_sizes)
        else:
            features['avg_packet_size'] = 0
            features['packet_size_std'] = 0
            
        # 包间隔特征
        inter_arrivals = flow_data.get('inter_arrival_times', [])
        if inter_arrivals:
            features['avg_inter_arrival'] = np.mean(inter_arrivals)
            features['inter_arrival_std'] = np.std(inter_arrivals)
        else:
            features['avg_inter_arrival'] = 0
            features['inter_arrival_std'] = 0
            
        # 速率特征
        features['packet_rate'] = features['packet_count'] / features['duration']
        features['byte_rate'] = features['byte_count'] / features['duration']
        
        return features
        
    def calculate_entropy(self, data, bins=10):
        """
        计算数据的信息熵
        
        Args:
            data: 输入数据列表
            bins: 直方图分箱数量
            
        Returns:
            float: 信息熵值
        """
        if not data or len(data) < 2:
            return 0.0
            
        # 将数据分箱
        hist, _ = np.histogram(data, bins=bins)
        
        # 计算概率分布
        probabilities = hist / np.sum(hist)
        
        # 过滤零概率
        probabilities = probabilities[probabilities > 0]
        
        # 计算熵
        entropy = -np.sum(probabilities * np.log2(probabilities))
        
        return entropy

    def calculate_shannon_entropy(self, data, adaptive_bins=True):
        """
        改进的香农熵计算

        Args:
            data: 输入数据
            adaptive_bins: 是否使用自适应分箱

        Returns:
            float: 香农熵值
        """
        if not data or len(data) < 2:
            return 0.0

        data_array = np.array(data)

        # 自适应分箱
        if adaptive_bins:
            # 使用Sturges规则或Scott规则
            n = len(data_array)
            bins = max(int(np.log2(n) + 1), 5)  # Sturges规则
        else:
            bins = 10

        # 处理连续值和离散值
        if len(np.unique(data_array)) < bins:
            # 离散值，直接计算概率
            unique_vals, counts = np.unique(data_array, return_counts=True)
            probabilities = counts / np.sum(counts)
        else:
            # 连续值，使用直方图
            hist, _ = np.histogram(data_array, bins=bins)
            probabilities = hist / np.sum(hist)

        # 过滤零概率
        probabilities = probabilities[probabilities > 0]

        # 计算香农熵
        entropy = -np.sum(probabilities * np.log2(probabilities))

        return entropy

    def calculate_conditional_entropy(self, data1, data2, bins=10):
        """
        计算条件熵 H(X|Y)

        Args:
            data1: X数据
            data2: Y数据
            bins: 分箱数量

        Returns:
            float: 条件熵值
        """
        if not data1 or not data2 or len(data1) != len(data2):
            return 0.0

        # 计算联合熵 H(X,Y)
        joint_hist, _, _ = np.histogram2d(data1, data2, bins=bins)
        joint_prob = joint_hist / np.sum(joint_hist)
        joint_prob = joint_prob[joint_prob > 0]
        joint_entropy = -np.sum(joint_prob * np.log2(joint_prob))

        # 计算边际熵 H(Y)
        marginal_entropy = self.calculate_shannon_entropy(data2)

        # 条件熵 H(X|Y) = H(X,Y) - H(Y)
        conditional_entropy = joint_entropy - marginal_entropy

        return max(conditional_entropy, 0.0)

    def calculate_kl_divergence(self, data1, data2, bins=10):
        """
        计算两个数据分布的KL散度
        
        Args:
            data1, data2: 两个数据分布
            bins: 分箱数量
            
        Returns:
            float: KL散度值
        """
        if not data1 or not data2:
            return 0.0
            
        # 确定共同的分箱范围
        min_val = min(min(data1), min(data2))
        max_val = max(max(data1), max(data2))
        bin_edges = np.linspace(min_val, max_val, bins + 1)
        
        # 计算直方图
        hist1, _ = np.histogram(data1, bins=bin_edges)
        hist2, _ = np.histogram(data2, bins=bin_edges)
        
        # 转换为概率分布（添加小的平滑项避免零概率）
        p = (hist1 + 1e-10) / (np.sum(hist1) + bins * 1e-10)
        q = (hist2 + 1e-10) / (np.sum(hist2) + bins * 1e-10)
        
        # 计算KL散度
        kl_div = np.sum(p * np.log(p / q))
        
        return kl_div
        
    def calculate_burstiness(self, inter_arrivals):
        """
        计算流量突发性指标
        
        Args:
            inter_arrivals: 包间隔时间列表
            
        Returns:
            float: 突发性指标
        """
        if not inter_arrivals or len(inter_arrivals) < 2:
            return 0.0
            
        mean_interval = np.mean(inter_arrivals)
        std_interval = np.std(inter_arrivals)
        
        if mean_interval == 0:
            return 0.0
            
        # 突发性 = (标准差 - 均值) / (标准差 + 均值)
        burstiness = (std_interval - mean_interval) / (std_interval + mean_interval)
        
        return burstiness

    def calculate_hurst_exponent(self, time_series):
        """
        计算Hurst指数（长程相关性）

        Args:
            time_series: 时间序列数据

        Returns:
            float: Hurst指数
        """
        if not time_series or len(time_series) < 10:
            return 0.5  # 默认值

        time_series = np.array(time_series)
        n = len(time_series)

        # 计算累积偏差
        mean_ts = np.mean(time_series)
        cumulative_deviate = np.cumsum(time_series - mean_ts)

        # 计算范围
        R = np.max(cumulative_deviate) - np.min(cumulative_deviate)

        # 计算标准差
        S = np.std(time_series)

        if S == 0:
            return 0.5

        # R/S统计
        rs = R / S

        # Hurst指数估计
        if rs > 0:
            hurst = np.log(rs) / np.log(n)
        else:
            hurst = 0.5

        return np.clip(hurst, 0, 1)

    def calculate_fractal_dimension(self, data):
        """
        计算分形维数

        Args:
            data: 输入数据

        Returns:
            float: 分形维数
        """
        if not data or len(data) < 4:
            return 1.0

        data = np.array(data)
        n = len(data)

        # 使用盒计数法的简化版本
        scales = np.logspace(0, np.log10(n//4), 10).astype(int)
        counts = []

        for scale in scales:
            if scale >= n:
                continue

            # 将数据分成scale大小的盒子
            boxes = n // scale
            box_counts = 0

            for i in range(boxes):
                start_idx = i * scale
                end_idx = min((i + 1) * scale, n)
                box_data = data[start_idx:end_idx]

                if len(np.unique(box_data)) > 1:
                    box_counts += 1

            counts.append(box_counts)

        if len(counts) < 2:
            return 1.0

        # 计算分形维数
        log_scales = np.log(scales[:len(counts)])
        log_counts = np.log(np.array(counts) + 1e-10)

        # 线性拟合（增加数值稳定性检查）
        if len(log_scales) > 1:
            try:
                # 检查数据有效性
                if np.any(np.isnan(log_scales)) or np.any(np.isnan(log_counts)):
                    fractal_dim = 1.0
                elif np.any(np.isinf(log_scales)) or np.any(np.isinf(log_counts)):
                    fractal_dim = 1.0
                else:
                    slope, _ = np.polyfit(log_scales, log_counts, 1)
                    fractal_dim = -slope
            except (np.linalg.LinAlgError, ValueError):
                # 如果线性拟合失败，返回默认值
                fractal_dim = 1.0
        else:
            fractal_dim = 1.0

        return np.clip(fractal_dim, 0, 2)

    def calculate_lyapunov_exponent(self, time_series):
        """
        计算李雅普诺夫指数（混沌特征）

        Args:
            time_series: 时间序列

        Returns:
            float: 李雅普诺夫指数
        """
        if not time_series or len(time_series) < 10:
            return 0.0

        time_series = np.array(time_series)
        n = len(time_series)

        # 简化的李雅普诺夫指数计算
        # 计算相邻点的距离变化
        distances = []

        for i in range(1, n-1):
            # 计算局部发散率
            if time_series[i-1] != 0:
                divergence = abs((time_series[i+1] - time_series[i]) / time_series[i-1])
                if divergence > 0:
                    distances.append(np.log(divergence))

        if not distances:
            return 0.0

        # 平均李雅普诺夫指数
        lyapunov = np.mean(distances)

        return lyapunov

    def extract_protocol_features(self, flow_data):
        """
        提取协议特定特征

        Args:
            flow_data: 流数据

        Returns:
            dict: 协议特征
        """
        features = {}

        # 从流标识中提取协议信息
        flow_key = flow_data.get('flow_key', '')
        if ':' in flow_key:
            protocol = flow_key.split(':')[-1]
        else:
            protocol = '0'

        # 协议类型特征
        features['is_tcp'] = 1 if protocol == '6' else 0
        features['is_udp'] = 1 if protocol == '17' else 0
        features['is_icmp'] = 1 if protocol == '1' else 0

        packet_sizes = flow_data.get('packet_sizes', [])
        inter_arrivals = flow_data.get('inter_arrival_times', [])

        # TCP特定特征
        if features['is_tcp']:
            # TCP窗口大小变化（模拟）
            if packet_sizes:
                features['tcp_window_variation'] = np.std(packet_sizes) / (np.mean(packet_sizes) + 1e-10)
            else:
                features['tcp_window_variation'] = 0

            # TCP重传率（模拟）
            features['tcp_retransmission_rate'] = 0.01  # 占位符

        # UDP特定特征
        elif features['is_udp']:
            # UDP突发模式
            if inter_arrivals:
                features['udp_burst_pattern'] = self.calculate_burstiness(inter_arrivals)
            else:
                features['udp_burst_pattern'] = 0

        # 默认值
        for key in ['tcp_window_variation', 'tcp_retransmission_rate', 'udp_burst_pattern']:
            if key not in features:
                features[key] = 0

        return features

    def extract_advanced_features(self, flow_data):
        """
        提取高级特征
        
        Args:
            flow_data: 流数据
            
        Returns:
            dict: 高级特征字典
        """
        features = {}
        
        packet_sizes = flow_data.get('packet_sizes', [])
        inter_arrivals = flow_data.get('inter_arrival_times', [])
        
        # 基础信息熵特征
        features['entropy_packet_size'] = self.calculate_shannon_entropy(packet_sizes)
        features['entropy_inter_arrival'] = self.calculate_shannon_entropy(inter_arrivals)

        # 高级统计特征
        features['hurst_exponent'] = self.calculate_hurst_exponent(inter_arrivals)
        features['fractal_dimension'] = self.calculate_fractal_dimension(packet_sizes)
        features['lyapunov_exponent'] = self.calculate_lyapunov_exponent(inter_arrivals)

        # 条件熵特征
        if len(packet_sizes) == len(inter_arrivals):
            features['conditional_entropy'] = self.calculate_conditional_entropy(packet_sizes, inter_arrivals)
        else:
            features['conditional_entropy'] = 0.0

        # 突发性特征
        features['burstiness'] = self.calculate_burstiness(inter_arrivals)

        # 协议特定特征
        protocol_features = self.extract_protocol_features(flow_data)
        features.update(protocol_features)

        # 流对称性（简化版本，实际需要双向流数据）
        features['flow_symmetry'] = 0.5  # 占位符，实际实现需要双向流信息

        return features
        
    def extract_all_features(self, flow_data):
        """
        提取所有特征
        
        Args:
            flow_data: 流数据
            
        Returns:
            dict: 完整特征字典
        """
        basic_features = self.extract_basic_features(flow_data)
        advanced_features = self.extract_advanced_features(flow_data)
        
        # 合并特征
        all_features = {**basic_features, **advanced_features}
        
        return all_features
        
    def extract_batch_features(self, flows_data):
        """
        批量提取多个流的特征
        
        Args:
            flows_data: 多个流的数据字典
            
        Returns:
            pandas.DataFrame: 特征矩阵
        """
        features_list = []
        flow_ids = []
        
        for flow_id, flow_data in flows_data.items():
            features = self.extract_all_features(flow_data)
            features_list.append(features)
            flow_ids.append(flow_id)
            
        # 转换为DataFrame
        df = pd.DataFrame(features_list, index=flow_ids)
        
        # 处理缺失值和异常值
        df = df.fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        return df
        
    def get_feature_vector(self, flow_data):
        """
        获取标准化的特征向量
        
        Args:
            flow_data: 流数据
            
        Returns:
            numpy.array: 特征向量
        """
        features = self.extract_all_features(flow_data)
        
        # 按照预定义顺序提取特征值
        feature_vector = []
        for feature_name in self.feature_names:
            feature_vector.append(features.get(feature_name, 0))
            
        return np.array(feature_vector)
        
    def normalize_features(self, feature_matrix):
        """
        标准化特征矩阵
        
        Args:
            feature_matrix: 特征矩阵
            
        Returns:
            numpy.array: 标准化后的特征矩阵
        """
        from sklearn.preprocessing import StandardScaler
        
        scaler = StandardScaler()
        normalized_features = scaler.fit_transform(feature_matrix)
        
        return normalized_features, scaler

    def feature_selection(self, feature_matrix, method='variance', threshold=0.1):
        """
        特征选择算法

        Args:
            feature_matrix: 特征矩阵
            method: 选择方法 ('variance', 'correlation')
            threshold: 阈值

        Returns:
            selected_features, selected_indices
        """
        if isinstance(feature_matrix, pd.DataFrame):
            data = feature_matrix.values
            feature_names = feature_matrix.columns.tolist()
        else:
            data = feature_matrix
            feature_names = [f"feature_{i}" for i in range(data.shape[1])]

        if method == 'variance':
            # 方差选择
            variances = np.var(data, axis=0)
            selected_indices = np.where(variances > threshold)[0]

        elif method == 'correlation':
            # 相关性选择（移除高相关特征）
            corr_matrix = np.corrcoef(data.T)
            selected_indices = []

            for i in range(len(feature_names)):
                is_redundant = False
                for j in selected_indices:
                    if abs(corr_matrix[i, j]) > threshold:
                        is_redundant = True
                        break
                if not is_redundant:
                    selected_indices.append(i)

            selected_indices = np.array(selected_indices)

        else:
            selected_indices = np.arange(data.shape[1])

        selected_features = [feature_names[i] for i in selected_indices]

        return selected_features, selected_indices

if __name__ == "__main__":
    # 测试代码
    extractor = FeatureExtractor()
    
    # 模拟流数据
    test_flow = {
        'packet_count': 100,
        'byte_count': 50000,
        'start_time': 1000.0,
        'last_time': 1010.0,
        'packet_sizes': np.random.normal(500, 100, 100).tolist(),
        'inter_arrival_times': np.random.exponential(0.1, 99).tolist()
    }
    
    # 提取特征
    features = extractor.extract_all_features(test_flow)
    print("提取的特征:")
    for key, value in features.items():
        print(f"  {key}: {value:.4f}")
        
    # 获取特征向量
    feature_vector = extractor.get_feature_vector(test_flow)
    print(f"\n特征向量: {feature_vector}")
