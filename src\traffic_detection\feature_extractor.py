"""
特征提取模块
Feature Extractor Module

功能：
- 从流量数据中提取关键特征
- 计算信息熵、K-L散度等统计特征
- 生成特征向量供机器学习模块使用
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.spatial.distance import j<PERSON><PERSON><PERSON><PERSON>
from collections import Counter
import math

class FeatureExtractor:
    """流量特征提取器"""
    
    def __init__(self):
        """初始化特征提取器"""
        self.feature_names = [
            'packet_count', 'byte_count', 'duration', 'avg_packet_size',
            'packet_size_std', 'avg_inter_arrival', 'inter_arrival_std',
            'packet_rate', 'byte_rate', 'entropy_packet_size', 
            'entropy_inter_arrival', 'burstiness', 'flow_symmetry'
        ]
        
    def extract_basic_features(self, flow_data):
        """
        提取基本流量特征
        
        Args:
            flow_data: 单个流的统计数据
            
        Returns:
            dict: 基本特征字典
        """
        features = {}
        
        # 基本统计特征
        features['packet_count'] = flow_data.get('packet_count', 0)
        features['byte_count'] = flow_data.get('byte_count', 0)
        
        # 时间特征
        start_time = flow_data.get('start_time', 0)
        last_time = flow_data.get('last_time', 0)
        features['duration'] = max(last_time - start_time, 0.001)  # 避免除零
        
        # 包大小特征
        packet_sizes = flow_data.get('packet_sizes', [])
        if packet_sizes:
            features['avg_packet_size'] = np.mean(packet_sizes)
            features['packet_size_std'] = np.std(packet_sizes)
        else:
            features['avg_packet_size'] = 0
            features['packet_size_std'] = 0
            
        # 包间隔特征
        inter_arrivals = flow_data.get('inter_arrival_times', [])
        if inter_arrivals:
            features['avg_inter_arrival'] = np.mean(inter_arrivals)
            features['inter_arrival_std'] = np.std(inter_arrivals)
        else:
            features['avg_inter_arrival'] = 0
            features['inter_arrival_std'] = 0
            
        # 速率特征
        features['packet_rate'] = features['packet_count'] / features['duration']
        features['byte_rate'] = features['byte_count'] / features['duration']
        
        return features
        
    def calculate_entropy(self, data, bins=10):
        """
        计算数据的信息熵
        
        Args:
            data: 输入数据列表
            bins: 直方图分箱数量
            
        Returns:
            float: 信息熵值
        """
        if not data or len(data) < 2:
            return 0.0
            
        # 将数据分箱
        hist, _ = np.histogram(data, bins=bins)
        
        # 计算概率分布
        probabilities = hist / np.sum(hist)
        
        # 过滤零概率
        probabilities = probabilities[probabilities > 0]
        
        # 计算熵
        entropy = -np.sum(probabilities * np.log2(probabilities))
        
        return entropy
        
    def calculate_kl_divergence(self, data1, data2, bins=10):
        """
        计算两个数据分布的KL散度
        
        Args:
            data1, data2: 两个数据分布
            bins: 分箱数量
            
        Returns:
            float: KL散度值
        """
        if not data1 or not data2:
            return 0.0
            
        # 确定共同的分箱范围
        min_val = min(min(data1), min(data2))
        max_val = max(max(data1), max(data2))
        bin_edges = np.linspace(min_val, max_val, bins + 1)
        
        # 计算直方图
        hist1, _ = np.histogram(data1, bins=bin_edges)
        hist2, _ = np.histogram(data2, bins=bin_edges)
        
        # 转换为概率分布（添加小的平滑项避免零概率）
        p = (hist1 + 1e-10) / (np.sum(hist1) + bins * 1e-10)
        q = (hist2 + 1e-10) / (np.sum(hist2) + bins * 1e-10)
        
        # 计算KL散度
        kl_div = np.sum(p * np.log(p / q))
        
        return kl_div
        
    def calculate_burstiness(self, inter_arrivals):
        """
        计算流量突发性指标
        
        Args:
            inter_arrivals: 包间隔时间列表
            
        Returns:
            float: 突发性指标
        """
        if not inter_arrivals or len(inter_arrivals) < 2:
            return 0.0
            
        mean_interval = np.mean(inter_arrivals)
        std_interval = np.std(inter_arrivals)
        
        if mean_interval == 0:
            return 0.0
            
        # 突发性 = (标准差 - 均值) / (标准差 + 均值)
        burstiness = (std_interval - mean_interval) / (std_interval + mean_interval)
        
        return burstiness
        
    def extract_advanced_features(self, flow_data):
        """
        提取高级特征
        
        Args:
            flow_data: 流数据
            
        Returns:
            dict: 高级特征字典
        """
        features = {}
        
        packet_sizes = flow_data.get('packet_sizes', [])
        inter_arrivals = flow_data.get('inter_arrival_times', [])
        
        # 信息熵特征
        features['entropy_packet_size'] = self.calculate_entropy(packet_sizes)
        features['entropy_inter_arrival'] = self.calculate_entropy(inter_arrivals)
        
        # 突发性特征
        features['burstiness'] = self.calculate_burstiness(inter_arrivals)
        
        # 流对称性（简化版本，实际需要双向流数据）
        features['flow_symmetry'] = 0.5  # 占位符，实际实现需要双向流信息
        
        return features
        
    def extract_all_features(self, flow_data):
        """
        提取所有特征
        
        Args:
            flow_data: 流数据
            
        Returns:
            dict: 完整特征字典
        """
        basic_features = self.extract_basic_features(flow_data)
        advanced_features = self.extract_advanced_features(flow_data)
        
        # 合并特征
        all_features = {**basic_features, **advanced_features}
        
        return all_features
        
    def extract_batch_features(self, flows_data):
        """
        批量提取多个流的特征
        
        Args:
            flows_data: 多个流的数据字典
            
        Returns:
            pandas.DataFrame: 特征矩阵
        """
        features_list = []
        flow_ids = []
        
        for flow_id, flow_data in flows_data.items():
            features = self.extract_all_features(flow_data)
            features_list.append(features)
            flow_ids.append(flow_id)
            
        # 转换为DataFrame
        df = pd.DataFrame(features_list, index=flow_ids)
        
        # 处理缺失值和异常值
        df = df.fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        return df
        
    def get_feature_vector(self, flow_data):
        """
        获取标准化的特征向量
        
        Args:
            flow_data: 流数据
            
        Returns:
            numpy.array: 特征向量
        """
        features = self.extract_all_features(flow_data)
        
        # 按照预定义顺序提取特征值
        feature_vector = []
        for feature_name in self.feature_names:
            feature_vector.append(features.get(feature_name, 0))
            
        return np.array(feature_vector)
        
    def normalize_features(self, feature_matrix):
        """
        标准化特征矩阵
        
        Args:
            feature_matrix: 特征矩阵
            
        Returns:
            numpy.array: 标准化后的特征矩阵
        """
        from sklearn.preprocessing import StandardScaler
        
        scaler = StandardScaler()
        normalized_features = scaler.fit_transform(feature_matrix)
        
        return normalized_features, scaler

if __name__ == "__main__":
    # 测试代码
    extractor = FeatureExtractor()
    
    # 模拟流数据
    test_flow = {
        'packet_count': 100,
        'byte_count': 50000,
        'start_time': 1000.0,
        'last_time': 1010.0,
        'packet_sizes': np.random.normal(500, 100, 100).tolist(),
        'inter_arrival_times': np.random.exponential(0.1, 99).tolist()
    }
    
    # 提取特征
    features = extractor.extract_all_features(test_flow)
    print("提取的特征:")
    for key, value in features.items():
        print(f"  {key}: {value:.4f}")
        
    # 获取特征向量
    feature_vector = extractor.get_feature_vector(test_flow)
    print(f"\n特征向量: {feature_vector}")
