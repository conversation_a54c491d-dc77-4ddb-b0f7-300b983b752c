#!/usr/bin/env python3
"""
性能压力测试脚本
测试系统在高负载下的表现
"""

import sys
import time
import threading
import requests
import concurrent.futures
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / 'src'))

def test_api_performance():
    """测试API性能"""
    print("🚀 开始API性能测试...")
    
    base_url = 'http://localhost:5000'
    endpoints = [
        '/api/realtime-stats',
        '/api/detection-results',
        '/api/traffic-features',
        '/api/performance-metrics'
    ]
    
    def make_request(endpoint):
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            end_time = time.time()
            return {
                'endpoint': endpoint,
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': response.status_code == 200
            }
        except Exception as e:
            return {
                'endpoint': endpoint,
                'status_code': 0,
                'response_time': 5.0,
                'success': False,
                'error': str(e)
            }
    
    # 并发测试
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for _ in range(50):  # 50个并发请求
            for endpoint in endpoints:
                futures.append(executor.submit(make_request, endpoint))
        
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    total_requests = len(results)
    avg_response_time = sum(r['response_time'] for r in results) / total_requests
    
    print(f"📊 API性能测试结果:")
    print(f"   总请求数: {total_requests}")
    print(f"   成功请求: {success_count}")
    print(f"   成功率: {success_count/total_requests*100:.1f}%")
    print(f"   平均响应时间: {avg_response_time:.3f}s")
    
    return success_count/total_requests >= 0.9

def test_detection_performance():
    """测试检测性能"""
    print("🔍 开始检测性能测试...")
    
    try:
        from member_b_detection.enhanced_traffic_collector import EnhancedTrafficCollector
        from member_b_detection.feature_extractor import FeatureExtractor
        from member_b_detection.dynamic_threshold import DynamicThreshold
        
        # 创建实例
        collector = EnhancedTrafficCollector()
        extractor = FeatureExtractor()
        detector = DynamicThreshold()
        
        # 启动采集
        collector.start_collection()
        
        # 性能测试
        start_time = time.time()
        
        # 生成大量流量数据
        for i in range(10):
            collector.simulate_attack_scenario('ddos', duration=1)
            collector.simulate_attack_scenario('port_scan', duration=1)
            time.sleep(0.1)
        
        # 获取流量统计
        flow_stats = collector.get_flow_statistics()
        flows_count = len(flow_stats)
        
        # 特征提取性能测试
        feature_start = time.time()
        if flow_stats:
            feature_matrix = extractor.extract_batch_features(flow_stats)
            feature_time = time.time() - feature_start
            
            # 检测性能测试
            detection_start = time.time()
            packet_counts = feature_matrix['packet_count'].values
            results = detector.batch_detect(packet_counts)
            detection_time = time.time() - detection_start
            
            total_time = time.time() - start_time
            
            print(f"📊 检测性能测试结果:")
            print(f"   处理流数量: {flows_count}")
            print(f"   特征提取时间: {feature_time:.3f}s")
            print(f"   异常检测时间: {detection_time:.3f}s")
            print(f"   总处理时间: {total_time:.3f}s")
            print(f"   处理速度: {flows_count/total_time:.1f} flows/s")
            
            return flows_count/total_time > 100  # 要求处理速度 > 100 flows/s
        else:
            print("❌ 未生成流量数据")
            return False
            
    except Exception as e:
        print(f"❌ 检测性能测试失败: {e}")
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("💾 开始内存使用测试...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 运行一段时间的检测
        from member_b_detection.enhanced_traffic_collector import EnhancedTrafficCollector
        collector = EnhancedTrafficCollector()
        collector.start_collection()
        
        for i in range(20):
            collector.simulate_attack_scenario('ddos', duration=0.5)
            time.sleep(0.1)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"📊 内存使用测试结果:")
        print(f"   初始内存: {initial_memory:.1f} MB")
        print(f"   最终内存: {final_memory:.1f} MB")
        print(f"   内存增长: {memory_increase:.1f} MB")
        
        return memory_increase < 100  # 内存增长应小于100MB
        
    except ImportError:
        print("⚠️ 需要安装psutil: pip install psutil")
        return True
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return False

def test_concurrent_users():
    """测试并发用户访问"""
    print("👥 开始并发用户测试...")
    
    def simulate_user():
        """模拟用户行为"""
        base_url = 'http://localhost:5000'
        try:
            # 模拟用户访问流程
            requests.get(f"{base_url}/", timeout=5)
            requests.get(f"{base_url}/api/realtime-stats", timeout=5)
            requests.post(f"{base_url}/api/generate-demo", timeout=10)
            requests.get(f"{base_url}/api/detection-results", timeout=5)
            return True
        except:
            return False
    
    # 模拟10个并发用户
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(simulate_user) for _ in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    success_rate = sum(results) / len(results)
    print(f"📊 并发用户测试结果:")
    print(f"   并发用户数: {len(results)}")
    print(f"   成功用户数: {sum(results)}")
    print(f"   成功率: {success_rate*100:.1f}%")
    
    return success_rate >= 0.8

def main():
    """主测试函数"""
    print("🧪 开始系统性能压力测试...")
    print("="*60)
    
    tests = [
        ("API性能测试", test_api_performance),
        ("检测性能测试", test_detection_performance),
        ("内存使用测试", test_memory_usage),
        ("并发用户测试", test_concurrent_users)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🔬 执行: {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status}: {test_name}")
        except Exception as e:
            results[test_name] = False
            print(f"💥 异常: {test_name} - {e}")
    
    # 总结
    print("\n" + "="*60)
    print("📊 性能测试总结")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n🎯 总体通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed/total >= 0.75:
        print("🎉 系统性能测试通过！")
    else:
        print("⚠️ 系统性能需要优化")

if __name__ == '__main__':
    main()
