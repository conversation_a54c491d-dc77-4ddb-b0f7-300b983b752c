#!/usr/bin/env python3
"""
模块协调器 - A成员
负责协调B、C、D成员的模块，实现系统整体协同工作
"""

import time
import json
import threading
import queue
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ModuleType(Enum):
    """模块类型枚举"""
    DETECTION = "detection"      # B成员检测模块
    ML = "ml"                   # C成员机器学习模块
    FRONTEND = "frontend"       # D成员前端模块
    CONTROLLER = "controller"   # A成员控制器模块

class MessageType(Enum):
    """消息类型枚举"""
    DATA = "data"
    COMMAND = "command"
    STATUS = "status"
    ALERT = "alert"
    HEARTBEAT = "heartbeat"

@dataclass
class ModuleMessage:
    """模块间消息数据结构"""
    msg_id: str
    msg_type: MessageType
    source_module: ModuleType
    target_module: ModuleType
    data: Dict[str, Any]
    timestamp: float
    priority: int = 1  # 1=低, 2=中, 3=高

class ModuleInterface:
    """模块接口基类"""
    
    def __init__(self, module_type: ModuleType, module_id: str):
        self.module_type = module_type
        self.module_id = module_id
        self.is_active = False
        self.last_heartbeat = 0.0
        self.message_handlers: Dict[MessageType, Callable] = {}
    
    def register_handler(self, msg_type: MessageType, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[msg_type] = handler
    
    def handle_message(self, message: ModuleMessage) -> Optional[ModuleMessage]:
        """处理消息"""
        if message.msg_type in self.message_handlers:
            return self.message_handlers[message.msg_type](message)
        return None
    
    def send_heartbeat(self) -> ModuleMessage:
        """发送心跳消息"""
        self.last_heartbeat = time.time()
        return ModuleMessage(
            msg_id=f"heartbeat_{self.module_id}_{int(time.time())}",
            msg_type=MessageType.HEARTBEAT,
            source_module=self.module_type,
            target_module=ModuleType.CONTROLLER,
            data={"status": "alive", "module_id": self.module_id},
            timestamp=self.last_heartbeat
        )
    
    def is_healthy(self) -> bool:
        """检查模块健康状态"""
        return self.is_active and (time.time() - self.last_heartbeat) < 30

class ModuleCoordinator:
    """模块协调器"""
    
    def __init__(self, coordinator_id: str = "main_coordinator"):
        """初始化协调器"""
        self.coordinator_id = coordinator_id
        self.modules: Dict[str, ModuleInterface] = {}
        self.message_queue = queue.PriorityQueue()
        self.message_history: List[ModuleMessage] = []
        
        # 运行状态
        self.is_running = False
        self.coordinator_thread = None
        self.heartbeat_thread = None
        
        # 统计信息
        self.stats = {
            'total_messages': 0,
            'processed_messages': 0,
            'failed_messages': 0,
            'active_modules': 0,
            'start_time': time.time()
        }
        
        # 数据流管道
        self.data_pipelines = {
            'traffic_detection': [],  # 流量检测数据流
            'ml_prediction': [],      # ML预测数据流
            'frontend_display': []    # 前端显示数据流
        }
        
        logger.info("模块协调器初始化完成")
    
    def start_coordinator(self):
        """启动协调器"""
        if self.is_running:
            logger.warning("协调器已在运行")
            return
        
        self.is_running = True
        
        # 启动消息处理线程
        self.coordinator_thread = threading.Thread(target=self._message_loop, daemon=True)
        self.coordinator_thread.start()
        
        # 启动心跳检查线程
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        
        logger.info("模块协调器已启动")
    
    def stop_coordinator(self):
        """停止协调器"""
        self.is_running = False
        
        if self.coordinator_thread:
            self.coordinator_thread.join(timeout=5)
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
            
        logger.info("模块协调器已停止")
    
    def register_module(self, module: ModuleInterface) -> bool:
        """注册模块"""
        try:
            module_key = f"{module.module_type.value}_{module.module_id}"
            self.modules[module_key] = module
            module.is_active = True
            
            # 设置默认消息处理器
            self._setup_default_handlers(module)
            
            logger.info(f"模块已注册: {module_key}")
            return True
            
        except Exception as e:
            logger.error(f"注册模块失败: {e}")
            return False
    
    def unregister_module(self, module_type: ModuleType, module_id: str) -> bool:
        """注销模块"""
        try:
            module_key = f"{module_type.value}_{module_id}"
            if module_key in self.modules:
                self.modules[module_key].is_active = False
                del self.modules[module_key]
                logger.info(f"模块已注销: {module_key}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"注销模块失败: {e}")
            return False
    
    def send_message(self, message: ModuleMessage):
        """发送消息"""
        try:
            # 添加到消息队列（优先级队列）
            priority = -message.priority  # 负数表示高优先级
            self.message_queue.put((priority, time.time(), message))
            self.stats['total_messages'] += 1
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
    
    def broadcast_message(self, message: ModuleMessage, exclude_source: bool = True):
        """广播消息"""
        try:
            for module_key, module in self.modules.items():
                if exclude_source and module.module_type == message.source_module:
                    continue
                
                # 创建针对每个模块的消息副本
                broadcast_msg = ModuleMessage(
                    msg_id=f"{message.msg_id}_broadcast_{module_key}",
                    msg_type=message.msg_type,
                    source_module=message.source_module,
                    target_module=module.module_type,
                    data=message.data.copy(),
                    timestamp=message.timestamp,
                    priority=message.priority
                )
                self.send_message(broadcast_msg)
                
        except Exception as e:
            logger.error(f"广播消息失败: {e}")
    
    def _message_loop(self):
        """消息处理主循环"""
        while self.is_running:
            try:
                # 获取消息（阻塞等待，超时1秒）
                try:
                    priority, timestamp, message = self.message_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # 处理消息
                self._process_message(message)
                self.message_queue.task_done()
                
            except Exception as e:
                logger.error(f"消息处理循环错误: {e}")
                time.sleep(1)
    
    def _process_message(self, message: ModuleMessage):
        """处理单个消息"""
        try:
            # 记录消息历史
            self.message_history.append(message)
            if len(self.message_history) > 1000:  # 限制历史记录数量
                self.message_history.pop(0)
            
            # 查找目标模块
            target_modules = [module for module in self.modules.values() 
                            if module.module_type == message.target_module and module.is_active]
            
            if not target_modules:
                logger.warning(f"未找到目标模块: {message.target_module.value}")
                self.stats['failed_messages'] += 1
                return
            
            # 处理消息
            for module in target_modules:
                try:
                    response = module.handle_message(message)
                    if response:
                        self.send_message(response)
                    
                    self.stats['processed_messages'] += 1
                    
                except Exception as e:
                    logger.error(f"模块 {module.module_id} 处理消息失败: {e}")
                    self.stats['failed_messages'] += 1
            
            # 处理数据流管道
            self._process_data_pipeline(message)
            
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            self.stats['failed_messages'] += 1
    
    def _process_data_pipeline(self, message: ModuleMessage):
        """处理数据流管道"""
        try:
            # 流量检测数据流：B成员 -> C成员 -> D成员
            if (message.source_module == ModuleType.DETECTION and 
                message.msg_type == MessageType.DATA):
                
                # 转发给ML模块
                ml_message = ModuleMessage(
                    msg_id=f"pipeline_ml_{message.msg_id}",
                    msg_type=MessageType.DATA,
                    source_module=ModuleType.DETECTION,
                    target_module=ModuleType.ML,
                    data=message.data.copy(),
                    timestamp=time.time(),
                    priority=2
                )
                self.send_message(ml_message)
                
                # 转发给前端模块
                frontend_message = ModuleMessage(
                    msg_id=f"pipeline_frontend_{message.msg_id}",
                    msg_type=MessageType.DATA,
                    source_module=ModuleType.DETECTION,
                    target_module=ModuleType.FRONTEND,
                    data=message.data.copy(),
                    timestamp=time.time(),
                    priority=2
                )
                self.send_message(frontend_message)
            
            # ML预测结果数据流：C成员 -> A成员控制器 -> D成员
            elif (message.source_module == ModuleType.ML and 
                  message.msg_type == MessageType.DATA):
                
                # 转发给控制器
                controller_message = ModuleMessage(
                    msg_id=f"pipeline_controller_{message.msg_id}",
                    msg_type=MessageType.DATA,
                    source_module=ModuleType.ML,
                    target_module=ModuleType.CONTROLLER,
                    data=message.data.copy(),
                    timestamp=time.time(),
                    priority=3  # 高优先级
                )
                self.send_message(controller_message)
                
                # 转发给前端
                frontend_message = ModuleMessage(
                    msg_id=f"pipeline_frontend_ml_{message.msg_id}",
                    msg_type=MessageType.DATA,
                    source_module=ModuleType.ML,
                    target_module=ModuleType.FRONTEND,
                    data=message.data.copy(),
                    timestamp=time.time(),
                    priority=2
                )
                self.send_message(frontend_message)
                
        except Exception as e:
            logger.error(f"处理数据流管道失败: {e}")
    
    def _heartbeat_loop(self):
        """心跳检查循环"""
        while self.is_running:
            try:
                current_time = time.time()
                active_count = 0
                
                for module_key, module in self.modules.items():
                    if module.is_healthy():
                        active_count += 1
                    else:
                        logger.warning(f"模块 {module_key} 心跳超时")
                        module.is_active = False
                
                self.stats['active_modules'] = active_count
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"心跳检查循环错误: {e}")
                time.sleep(30)
    
    def _setup_default_handlers(self, module: ModuleInterface):
        """设置默认消息处理器"""
        def default_heartbeat_handler(message: ModuleMessage) -> Optional[ModuleMessage]:
            """默认心跳处理器"""
            module.last_heartbeat = message.timestamp
            return None
        
        def default_status_handler(message: ModuleMessage) -> Optional[ModuleMessage]:
            """默认状态处理器"""
            logger.info(f"收到状态消息: {message.source_module.value} -> {message.data}")
            return None
        
        module.register_handler(MessageType.HEARTBEAT, default_heartbeat_handler)
        module.register_handler(MessageType.STATUS, default_status_handler)
    
    def get_coordinator_status(self) -> Dict[str, Any]:
        """获取协调器状态"""
        return {
            'coordinator_id': self.coordinator_id,
            'is_running': self.is_running,
            'stats': self.stats.copy(),
            'modules': {
                module_key: {
                    'type': module.module_type.value,
                    'id': module.module_id,
                    'is_active': module.is_active,
                    'is_healthy': module.is_healthy(),
                    'last_heartbeat': module.last_heartbeat
                }
                for module_key, module in self.modules.items()
            },
            'queue_size': self.message_queue.qsize(),
            'message_history_size': len(self.message_history)
        }
    
    def get_message_statistics(self) -> Dict[str, Any]:
        """获取消息统计"""
        return {
            'total_messages': self.stats['total_messages'],
            'processed_messages': self.stats['processed_messages'],
            'failed_messages': self.stats['failed_messages'],
            'success_rate': (self.stats['processed_messages'] / max(1, self.stats['total_messages'])) * 100,
            'queue_size': self.message_queue.qsize(),
            'recent_messages': [
                {
                    'msg_id': msg.msg_id,
                    'type': msg.msg_type.value,
                    'source': msg.source_module.value,
                    'target': msg.target_module.value,
                    'timestamp': msg.timestamp
                }
                for msg in self.message_history[-10:]  # 最近10条消息
            ]
        }
    
    def create_detection_pipeline(self):
        """创建检测数据流管道"""
        # B成员检测 -> C成员ML -> A成员控制器 -> D成员前端
        pipeline_config = {
            'name': 'traffic_detection_pipeline',
            'stages': [
                {'module': ModuleType.DETECTION, 'function': 'extract_features'},
                {'module': ModuleType.ML, 'function': 'predict_threat'},
                {'module': ModuleType.CONTROLLER, 'function': 'generate_response'},
                {'module': ModuleType.FRONTEND, 'function': 'display_alert'}
            ]
        }
        
        self.data_pipelines['traffic_detection'] = pipeline_config
        logger.info("检测数据流管道已创建")
    
    def trigger_emergency_response(self, threat_data: Dict[str, Any]):
        """触发紧急响应"""
        emergency_message = ModuleMessage(
            msg_id=f"emergency_{int(time.time())}",
            msg_type=MessageType.ALERT,
            source_module=ModuleType.CONTROLLER,
            target_module=ModuleType.CONTROLLER,  # 广播给所有模块
            data={
                'alert_type': 'emergency',
                'threat_data': threat_data,
                'response_required': True
            },
            timestamp=time.time(),
            priority=3  # 最高优先级
        )
        
        self.broadcast_message(emergency_message, exclude_source=False)
        logger.critical(f"紧急响应已触发: {threat_data}")

if __name__ == "__main__":
    # 测试代码
    coordinator = ModuleCoordinator()
    coordinator.start_coordinator()
    
    # 创建测试模块
    test_module = ModuleInterface(ModuleType.DETECTION, "test_detector")
    coordinator.register_module(test_module)
    
    try:
        time.sleep(5)
        status = coordinator.get_coordinator_status()
        print(f"协调器状态: {json.dumps(status, indent=2, default=str)}")
        
        # 发送测试消息
        test_message = ModuleMessage(
            msg_id="test_001",
            msg_type=MessageType.DATA,
            source_module=ModuleType.DETECTION,
            target_module=ModuleType.ML,
            data={"test": "data"},
            timestamp=time.time()
        )
        coordinator.send_message(test_message)
        
        time.sleep(2)
        stats = coordinator.get_message_statistics()
        print(f"消息统计: {json.dumps(stats, indent=2, default=str)}")
        
    finally:
        coordinator.stop_coordinator()
