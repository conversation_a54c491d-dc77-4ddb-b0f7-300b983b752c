{"_from": "parseurl@~1.3.3", "_id": "parseurl@1.3.3", "_inBundle": false, "_integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "_location": "/parseurl", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parseurl@~1.3.3", "name": "parseurl", "escapedName": "parseurl", "rawSpec": "~1.3.3", "saveSpec": null, "fetchSpec": "~1.3.3"}, "_requiredBy": ["/express", "/finalhandler", "/serve-static"], "_resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "_shasum": "9da19e7bee8d12dff0513ed5b76957793bc2e8d4", "_spec": "parseurl@~1.3.3", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\express", "bugs": {"url": "https://github.com/pillarjs/parseurl/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "deprecated": false, "description": "parse a url with memoization", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.1", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "fast-url-parser": "1.1.3", "istanbul": "0.4.5", "mocha": "6.1.3"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/pillarjs/parseurl#readme", "license": "MIT", "name": "parseurl", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/parseurl.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --check-leaks --bail --reporter spec test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec test/"}, "version": "1.3.3"}