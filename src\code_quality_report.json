{"timestamp": "2025-06-09T22:47:51.987932", "project_path": "C:\\4th_semester\\BEP1\\src", "modules": {"comprehensive_demo.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\comprehensive_demo.py", "total_lines": 345, "code_lines": 238, "comment_lines": 34, "blank_lines": 73, "functions": 7, "classes": 1, "imports": 9, "docstrings": 7, "has_main": true, "comment_ratio": 0.09855072463768116, "docstring_ratio": 1.0}, "dynamic_threshold.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\dynamic_threshold.py", "total_lines": 573, "code_lines": 416, "comment_lines": 34, "blank_lines": 123, "functions": 22, "classes": 4, "imports": 8, "docstrings": 21, "has_main": true, "comment_ratio": 0.059336823734729496, "docstring_ratio": 0.9545454545454546}, "feature_extractor.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\feature_extractor.py", "total_lines": 617, "code_lines": 410, "comment_lines": 63, "blank_lines": 144, "functions": 17, "classes": 1, "imports": 7, "docstrings": 17, "has_main": true, "comment_ratio": 0.10210696920583469, "docstring_ratio": 1.0}, "performance_test.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\performance_test.py", "total_lines": 349, "code_lines": 240, "comment_lines": 27, "blank_lines": 82, "functions": 10, "classes": 1, "imports": 13, "docstrings": 10, "has_main": true, "comment_ratio": 0.07736389684813753, "docstring_ratio": 1.0}, "statistical_analyzer.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\statistical_analyzer.py", "total_lines": 564, "code_lines": 416, "comment_lines": 40, "blank_lines": 108, "functions": 14, "classes": 1, "imports": 10, "docstrings": 14, "has_main": true, "comment_ratio": 0.07092198581560284, "docstring_ratio": 1.0}, "test_modules.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\test_modules.py", "total_lines": 338, "code_lines": 223, "comment_lines": 40, "blank_lines": 75, "functions": 7, "classes": 0, "imports": 9, "docstrings": 7, "has_main": true, "comment_ratio": 0.11834319526627218, "docstring_ratio": 1.0}, "traffic_collector.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\traffic_collector.py", "total_lines": 337, "code_lines": 256, "comment_lines": 24, "blank_lines": 57, "functions": 12, "classes": 1, "imports": 9, "docstrings": 12, "has_main": true, "comment_ratio": 0.0712166172106825, "docstring_ratio": 1.0}, "__init__.py": {"file_path": "C:\\4th_semester\\BEP1\\src\\traffic_detection\\__init__.py", "total_lines": 28, "code_lines": 23, "comment_lines": 1, "blank_lines": 4, "functions": 0, "classes": 0, "imports": 4, "docstrings": 0, "has_main": false, "comment_ratio": 0.03571428571428571, "docstring_ratio": 0}}, "summary": {"existing_files": ["__init__.py", "traffic_collector.py", "feature_extractor.py", "dynamic_threshold.py", "statistical_analyzer.py", "test_modules.py", "comprehensive_demo.py", "performance_test.py"], "missing_files": [], "dependencies": {"requirements_exists": true, "readme_exists": true, "requirements_size": 895, "readme_size": 5558}, "code_stats": {"total_files": 8, "total_lines": 3151, "total_functions": 89, "total_classes": 9, "avg_comment_ratio": 0.07919431230415326, "avg_docstring_ratio": 0.8693181818181819}, "overall_score": 80}, "issues": [], "recommendations": ["建议增加代码注释，目标注释率 >15%", "文档字符串覆盖率优秀", "代码规模适中"]}