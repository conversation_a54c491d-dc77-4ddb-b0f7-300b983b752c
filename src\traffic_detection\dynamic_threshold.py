"""
动态阈值检测模块
Dynamic Threshold Detection Module

功能：
- 实现自适应阈值算法
- 滑动窗口统计分析
- 动态调节检测灵敏度
- 异常检测和告警
"""

import numpy as np
import pandas as pd
from collections import deque
import time
import json

class DynamicThreshold:
    """动态阈值检测器"""
    
    def __init__(self, window_size=100, sensitivity=2.0, adaptation_rate=0.1):
        """
        初始化动态阈值检测器
        
        Args:
            window_size: 滑动窗口大小
            sensitivity: 检测灵敏度（标准差倍数）
            adaptation_rate: 阈值适应速率
        """
        self.window_size = window_size
        self.sensitivity = sensitivity
        self.adaptation_rate = adaptation_rate
        
        # 滑动窗口存储历史数据
        self.data_window = deque(maxlen=window_size)
        self.threshold_history = deque(maxlen=window_size)
        
        # 统计信息
        self.current_mean = 0.0
        self.current_std = 1.0
        self.current_threshold = 0.0
        
        # 检测结果记录
        self.detection_results = []
        self.anomaly_count = 0
        self.total_count = 0
        
    def update_statistics(self):
        """更新统计信息"""
        if len(self.data_window) < 2:
            return
            
        data_array = np.array(self.data_window)
        
        # 计算当前均值和标准差
        new_mean = np.mean(data_array)
        new_std = np.std(data_array)
        
        # 使用指数移动平均进行平滑
        if self.total_count > 0:
            self.current_mean = (1 - self.adaptation_rate) * self.current_mean + \
                               self.adaptation_rate * new_mean
            self.current_std = (1 - self.adaptation_rate) * self.current_std + \
                              self.adaptation_rate * new_std
        else:
            self.current_mean = new_mean
            self.current_std = new_std
            
        # 确保标准差不为零
        self.current_std = max(self.current_std, 0.001)
        
        # 更新阈值
        self.current_threshold = self.current_mean + self.sensitivity * self.current_std
        
    def detect_anomaly(self, value, timestamp=None):
        """
        检测异常值
        
        Args:
            value: 待检测的数值
            timestamp: 时间戳
            
        Returns:
            dict: 检测结果
        """
        if timestamp is None:
            timestamp = time.time()
            
        # 添加到滑动窗口
        self.data_window.append(value)
        self.total_count += 1
        
        # 更新统计信息
        self.update_statistics()
        
        # 异常检测
        is_anomaly = value > self.current_threshold
        
        if is_anomaly:
            self.anomaly_count += 1
            
        # 记录检测结果
        result = {
            'timestamp': timestamp,
            'value': value,
            'threshold': self.current_threshold,
            'mean': self.current_mean,
            'std': self.current_std,
            'is_anomaly': is_anomaly,
            'anomaly_score': (value - self.current_mean) / self.current_std
        }
        
        self.detection_results.append(result)
        self.threshold_history.append(self.current_threshold)
        
        return result
        
    def batch_detect(self, values, timestamps=None):
        """
        批量检测异常
        
        Args:
            values: 数值列表
            timestamps: 时间戳列表
            
        Returns:
            list: 检测结果列表
        """
        if timestamps is None:
            timestamps = [time.time() + i for i in range(len(values))]
            
        results = []
        for value, timestamp in zip(values, timestamps):
            result = self.detect_anomaly(value, timestamp)
            results.append(result)
            
        return results
        
    def adjust_sensitivity(self, new_sensitivity):
        """
        调整检测灵敏度
        
        Args:
            new_sensitivity: 新的灵敏度值
        """
        self.sensitivity = new_sensitivity
        self.update_statistics()
        print(f"检测灵敏度已调整为: {new_sensitivity}")
        
    def get_detection_stats(self):
        """
        获取检测统计信息
        
        Returns:
            dict: 统计信息
        """
        if self.total_count == 0:
            return {
                'total_count': 0,
                'anomaly_count': 0,
                'anomaly_rate': 0.0,
                'current_threshold': 0.0,
                'current_mean': 0.0,
                'current_std': 0.0
            }
            
        return {
            'total_count': self.total_count,
            'anomaly_count': self.anomaly_count,
            'anomaly_rate': self.anomaly_count / self.total_count,
            'current_threshold': self.current_threshold,
            'current_mean': self.current_mean,
            'current_std': self.current_std,
            'sensitivity': self.sensitivity,
            'window_size': self.window_size
        }
        
    def get_recent_results(self, n=10):
        """
        获取最近的检测结果
        
        Args:
            n: 返回结果数量
            
        Returns:
            list: 最近的检测结果
        """
        return self.detection_results[-n:] if self.detection_results else []
        
    def reset(self):
        """重置检测器状态"""
        self.data_window.clear()
        self.threshold_history.clear()
        self.detection_results.clear()
        
        self.current_mean = 0.0
        self.current_std = 1.0
        self.current_threshold = 0.0
        self.anomaly_count = 0
        self.total_count = 0
        
        print("动态阈值检测器已重置")
        
    def export_results(self, filename):
        """
        导出检测结果到文件
        
        Args:
            filename: 输出文件名
        """
        export_data = {
            'detection_results': self.detection_results,
            'statistics': self.get_detection_stats(),
            'parameters': {
                'window_size': self.window_size,
                'sensitivity': self.sensitivity,
                'adaptation_rate': self.adaptation_rate
            },
            'export_time': time.time()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
            
        print(f"检测结果已导出到: {filename}")

class MultiFeatureThreshold:
    """多特征动态阈值检测器"""
    
    def __init__(self, feature_names, window_size=100, sensitivity=2.0):
        """
        初始化多特征检测器
        
        Args:
            feature_names: 特征名称列表
            window_size: 滑动窗口大小
            sensitivity: 检测灵敏度
        """
        self.feature_names = feature_names
        self.detectors = {}
        
        # 为每个特征创建独立的检测器
        for feature_name in feature_names:
            self.detectors[feature_name] = DynamicThreshold(
                window_size=window_size,
                sensitivity=sensitivity
            )
            
    def detect_multi_features(self, feature_vector, feature_names=None, timestamp=None):
        """
        多特征异常检测
        
        Args:
            feature_vector: 特征向量
            feature_names: 特征名称（可选）
            timestamp: 时间戳
            
        Returns:
            dict: 多特征检测结果
        """
        if feature_names is None:
            feature_names = self.feature_names
            
        if timestamp is None:
            timestamp = time.time()
            
        results = {}
        anomaly_scores = []
        
        for i, (feature_name, value) in enumerate(zip(feature_names, feature_vector)):
            if feature_name in self.detectors:
                result = self.detectors[feature_name].detect_anomaly(value, timestamp)
                results[feature_name] = result
                anomaly_scores.append(abs(result['anomaly_score']))
                
        # 计算综合异常分数
        overall_anomaly_score = np.mean(anomaly_scores) if anomaly_scores else 0.0
        
        # 判断是否为异常（任一特征异常或综合分数过高）
        is_overall_anomaly = any(r['is_anomaly'] for r in results.values()) or \
                            overall_anomaly_score > 2.0
        
        return {
            'timestamp': timestamp,
            'feature_results': results,
            'overall_anomaly_score': overall_anomaly_score,
            'is_overall_anomaly': is_overall_anomaly,
            'anomaly_features': [name for name, result in results.items() 
                               if result['is_anomaly']]
        }
        
    def get_all_stats(self):
        """获取所有特征的统计信息"""
        stats = {}
        for feature_name, detector in self.detectors.items():
            stats[feature_name] = detector.get_detection_stats()
        return stats

if __name__ == "__main__":
    # 测试代码
    detector = DynamicThreshold(window_size=50, sensitivity=2.0)
    
    # 生成测试数据（正常数据 + 异常数据）
    normal_data = np.random.normal(100, 10, 80)
    anomaly_data = np.random.normal(150, 5, 20)
    test_data = np.concatenate([normal_data, anomaly_data])
    
    # 批量检测
    results = detector.batch_detect(test_data)
    
    # 输出统计信息
    stats = detector.get_detection_stats()
    print("检测统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
        
    # 输出最近的异常检测结果
    recent_anomalies = [r for r in detector.get_recent_results(10) if r['is_anomaly']]
    print(f"\n最近检测到 {len(recent_anomalies)} 个异常")
