"""
动态阈值检测模块
Dynamic Threshold Detection Module

功能：
- 实现自适应阈值算法
- 滑动窗口统计分析
- 动态调节检测灵敏度
- 异常检测和告警
"""

import numpy as np
import pandas as pd
from collections import deque
import time
import json

class DynamicThreshold:
    """动态阈值检测器"""
    
    def __init__(self, window_size=100, sensitivity=2.0, adaptation_rate=0.1):
        """
        初始化动态阈值检测器
        
        Args:
            window_size: 滑动窗口大小
            sensitivity: 检测灵敏度（标准差倍数）
            adaptation_rate: 阈值适应速率
        """
        self.window_size = window_size
        self.sensitivity = sensitivity
        self.adaptation_rate = adaptation_rate
        
        # 滑动窗口存储历史数据
        self.data_window = deque(maxlen=window_size)
        self.threshold_history = deque(maxlen=window_size)
        
        # 统计信息
        self.current_mean = 0.0
        self.current_std = 1.0
        self.current_threshold = 0.0
        
        # 检测结果记录
        self.detection_results = []
        self.anomaly_count = 0
        self.total_count = 0
        
    def update_statistics(self):
        """更新统计信息"""
        if len(self.data_window) < 2:
            return
            
        data_array = np.array(self.data_window)
        
        # 计算当前均值和标准差
        new_mean = np.mean(data_array)
        new_std = np.std(data_array)
        
        # 使用指数移动平均进行平滑
        if self.total_count > 0:
            self.current_mean = (1 - self.adaptation_rate) * self.current_mean + \
                               self.adaptation_rate * new_mean
            self.current_std = (1 - self.adaptation_rate) * self.current_std + \
                              self.adaptation_rate * new_std
        else:
            self.current_mean = new_mean
            self.current_std = new_std
            
        # 确保标准差不为零
        self.current_std = max(self.current_std, 0.001)
        
        # 更新阈值
        self.current_threshold = self.current_mean + self.sensitivity * self.current_std
        
    def detect_anomaly(self, value, timestamp=None):
        """
        检测异常值
        
        Args:
            value: 待检测的数值
            timestamp: 时间戳
            
        Returns:
            dict: 检测结果
        """
        if timestamp is None:
            timestamp = time.time()
            
        # 添加到滑动窗口
        self.data_window.append(value)
        self.total_count += 1
        
        # 更新统计信息
        self.update_statistics()
        
        # 异常检测
        is_anomaly = value > self.current_threshold
        
        if is_anomaly:
            self.anomaly_count += 1
            
        # 记录检测结果
        result = {
            'timestamp': timestamp,
            'value': value,
            'threshold': self.current_threshold,
            'mean': self.current_mean,
            'std': self.current_std,
            'is_anomaly': is_anomaly,
            'anomaly_score': (value - self.current_mean) / self.current_std
        }
        
        self.detection_results.append(result)
        self.threshold_history.append(self.current_threshold)
        
        return result
        
    def batch_detect(self, values, timestamps=None):
        """
        批量检测异常
        
        Args:
            values: 数值列表
            timestamps: 时间戳列表
            
        Returns:
            list: 检测结果列表
        """
        if timestamps is None:
            timestamps = [time.time() + i for i in range(len(values))]
            
        results = []
        for value, timestamp in zip(values, timestamps):
            result = self.detect_anomaly(value, timestamp)
            results.append(result)
            
        return results
        
    def adjust_sensitivity(self, new_sensitivity):
        """
        调整检测灵敏度
        
        Args:
            new_sensitivity: 新的灵敏度值
        """
        self.sensitivity = new_sensitivity
        self.update_statistics()
        print(f"检测灵敏度已调整为: {new_sensitivity}")
        
    def get_detection_stats(self):
        """
        获取检测统计信息
        
        Returns:
            dict: 统计信息
        """
        if self.total_count == 0:
            return {
                'total_count': 0,
                'anomaly_count': 0,
                'anomaly_rate': 0.0,
                'current_threshold': 0.0,
                'current_mean': 0.0,
                'current_std': 0.0
            }
            
        return {
            'total_count': self.total_count,
            'anomaly_count': self.anomaly_count,
            'anomaly_rate': self.anomaly_count / self.total_count,
            'current_threshold': self.current_threshold,
            'current_mean': self.current_mean,
            'current_std': self.current_std,
            'sensitivity': self.sensitivity,
            'window_size': self.window_size
        }
        
    def get_recent_results(self, n=10):
        """
        获取最近的检测结果
        
        Args:
            n: 返回结果数量
            
        Returns:
            list: 最近的检测结果
        """
        return self.detection_results[-n:] if self.detection_results else []
        
    def reset(self):
        """重置检测器状态"""
        self.data_window.clear()
        self.threshold_history.clear()
        self.detection_results.clear()
        
        self.current_mean = 0.0
        self.current_std = 1.0
        self.current_threshold = 0.0
        self.anomaly_count = 0
        self.total_count = 0
        
        print("动态阈值检测器已重置")

    def optimize_threshold_ml(self, historical_data, labels):
        """
        使用机器学习优化阈值

        Args:
            historical_data: 历史数据
            labels: 真实标签 (0: 正常, 1: 异常)

        Returns:
            optimal_threshold: 最优阈值
        """
        from sklearn.metrics import roc_curve, auc

        if len(historical_data) != len(labels):
            print("数据和标签长度不匹配")
            return self.current_threshold

        # 计算异常分数
        scores = []
        for value in historical_data:
            if self.current_std > 0:
                score = abs(value - self.current_mean) / self.current_std
            else:
                score = 0
            scores.append(score)

        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(labels, scores)

        # 找到最优阈值（最大化Youden指数）
        youden_index = tpr - fpr
        optimal_idx = np.argmax(youden_index)
        optimal_threshold = thresholds[optimal_idx]

        # 更新阈值
        old_sensitivity = self.sensitivity
        self.sensitivity = optimal_threshold

        print(f"阈值优化: {old_sensitivity:.3f} -> {optimal_threshold:.3f}")
        print(f"AUC: {auc(fpr, tpr):.3f}")

        return optimal_threshold

    def detect_seasonality(self, time_series, period=24):
        """
        检测时间序列的季节性

        Args:
            time_series: 时间序列数据
            period: 季节周期

        Returns:
            seasonality_score: 季节性分数
        """
        if len(time_series) < 2 * period:
            return 0.0

        time_series = np.array(time_series)

        # 计算自相关函数
        def autocorr(x, lag):
            if lag >= len(x):
                return 0
            c0 = np.var(x)
            c_lag = np.mean((x[:-lag] - np.mean(x)) * (x[lag:] - np.mean(x)))
            return c_lag / c0 if c0 > 0 else 0

        # 计算周期性自相关
        seasonal_autocorr = autocorr(time_series, period)

        return abs(seasonal_autocorr)

    def trend_aware_threshold(self, data, trend_component=None):
        """
        考虑趋势的阈值调整

        Args:
            data: 数据序列
            trend_component: 趋势分量

        Returns:
            adjusted_threshold: 调整后的阈值
        """
        if trend_component is None:
            # 简单趋势估计
            if len(data) < 3:
                trend_component = 0
            else:
                x = np.arange(len(data))
                trend_component = np.polyfit(x, data, 1)[0]  # 线性趋势

        # 根据趋势调整阈值
        trend_factor = 1 + abs(trend_component) * 0.1  # 趋势越强，阈值越宽松
        adjusted_threshold = self.current_threshold * trend_factor

        return adjusted_threshold

    def export_results(self, filename):
        """
        导出检测结果到文件
        
        Args:
            filename: 输出文件名
        """
        export_data = {
            'detection_results': self.detection_results,
            'statistics': self.get_detection_stats(),
            'parameters': {
                'window_size': self.window_size,
                'sensitivity': self.sensitivity,
                'adaptation_rate': self.adaptation_rate
            },
            'export_time': time.time()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
            
        print(f"检测结果已导出到: {filename}")

class MultiFeatureThreshold:
    """多特征动态阈值检测器"""
    
    def __init__(self, feature_names, window_size=100, sensitivity=2.0):
        """
        初始化多特征检测器
        
        Args:
            feature_names: 特征名称列表
            window_size: 滑动窗口大小
            sensitivity: 检测灵敏度
        """
        self.feature_names = feature_names
        self.detectors = {}
        
        # 为每个特征创建独立的检测器
        for feature_name in feature_names:
            self.detectors[feature_name] = DynamicThreshold(
                window_size=window_size,
                sensitivity=sensitivity
            )
            
    def detect_multi_features(self, feature_vector, feature_names=None, timestamp=None):
        """
        多特征异常检测
        
        Args:
            feature_vector: 特征向量
            feature_names: 特征名称（可选）
            timestamp: 时间戳
            
        Returns:
            dict: 多特征检测结果
        """
        if feature_names is None:
            feature_names = self.feature_names
            
        if timestamp is None:
            timestamp = time.time()
            
        results = {}
        anomaly_scores = []
        
        for i, (feature_name, value) in enumerate(zip(feature_names, feature_vector)):
            if feature_name in self.detectors:
                result = self.detectors[feature_name].detect_anomaly(value, timestamp)
                results[feature_name] = result
                anomaly_scores.append(abs(result['anomaly_score']))
                
        # 计算综合异常分数
        overall_anomaly_score = np.mean(anomaly_scores) if anomaly_scores else 0.0
        
        # 判断是否为异常（任一特征异常或综合分数过高）
        is_overall_anomaly = any(r['is_anomaly'] for r in results.values()) or \
                            overall_anomaly_score > 2.0
        
        return {
            'timestamp': timestamp,
            'feature_results': results,
            'overall_anomaly_score': overall_anomaly_score,
            'is_overall_anomaly': is_overall_anomaly,
            'anomaly_features': [name for name, result in results.items() 
                               if result['is_anomaly']]
        }
        
    def get_all_stats(self):
        """获取所有特征的统计信息"""
        stats = {}
        for feature_name, detector in self.detectors.items():
            stats[feature_name] = detector.get_detection_stats()
        return stats

class HierarchicalThreshold:
    """分层阈值检测"""

    def __init__(self, window_sizes=[50, 100, 200], sensitivities=[1.5, 2.0, 2.5]):
        """
        初始化分层阈值检测器

        Args:
            window_sizes: 不同层次的窗口大小
            sensitivities: 不同层次的灵敏度
        """
        self.detectors = []
        for window_size, sensitivity in zip(window_sizes, sensitivities):
            detector = DynamicThreshold(window_size=window_size, sensitivity=sensitivity)
            self.detectors.append(detector)

    def detect_multi_level(self, value, timestamp=None):
        """
        多层次异常检测

        Args:
            value: 待检测值
            timestamp: 时间戳

        Returns:
            dict: 多层次检测结果
        """
        results = []

        for i, detector in enumerate(self.detectors):
            result = detector.detect_anomaly(value, timestamp)
            result['level'] = i + 1
            results.append(result)

        # 综合判断
        anomaly_levels = [r['is_anomaly'] for r in results]
        overall_anomaly = any(anomaly_levels)

        # 异常严重程度
        severity = sum(anomaly_levels) / len(anomaly_levels)

        return {
            'timestamp': timestamp or time.time(),
            'value': value,
            'level_results': results,
            'is_overall_anomaly': overall_anomaly,
            'severity': severity
        }

class AdvancedAnomalyDetector:
    """高级异常检测器"""

    def __init__(self):
        """初始化高级异常检测器"""
        self.models = {}

    def isolation_forest_detection(self, features, contamination=0.1):
        """
        孤立森林异常检测

        Args:
            features: 特征矩阵
            contamination: 异常比例

        Returns:
            anomaly_scores, predictions
        """
        try:
            from sklearn.ensemble import IsolationForest

            if 'isolation_forest' not in self.models:
                self.models['isolation_forest'] = IsolationForest(
                    contamination=contamination,
                    random_state=42
                )

            model = self.models['isolation_forest']

            # 训练和预测
            predictions = model.fit_predict(features)
            anomaly_scores = model.decision_function(features)

            # 转换预测结果 (-1: 异常, 1: 正常) -> (1: 异常, 0: 正常)
            predictions = (predictions == -1).astype(int)

            return anomaly_scores, predictions

        except ImportError:
            print("scikit-learn未安装，无法使用孤立森林")
            return np.zeros(len(features)), np.zeros(len(features))

    def one_class_svm_detection(self, features, nu=0.1):
        """
        单类SVM异常检测

        Args:
            features: 特征矩阵
            nu: 异常比例上界

        Returns:
            anomaly_scores, predictions
        """
        try:
            from sklearn.svm import OneClassSVM

            if 'one_class_svm' not in self.models:
                self.models['one_class_svm'] = OneClassSVM(nu=nu, gamma='scale')

            model = self.models['one_class_svm']

            # 训练和预测
            predictions = model.fit_predict(features)
            anomaly_scores = model.decision_function(features)

            # 转换预测结果
            predictions = (predictions == -1).astype(int)

            return anomaly_scores, predictions

        except ImportError:
            print("scikit-learn未安装，无法使用单类SVM")
            return np.zeros(len(features)), np.zeros(len(features))

    def ensemble_detection(self, features, methods=['isolation_forest', 'one_class_svm']):
        """
        集成异常检测

        Args:
            features: 特征矩阵
            methods: 使用的方法列表

        Returns:
            ensemble_scores, ensemble_predictions
        """
        all_scores = []
        all_predictions = []

        for method in methods:
            if method == 'isolation_forest':
                scores, preds = self.isolation_forest_detection(features)
            elif method == 'one_class_svm':
                scores, preds = self.one_class_svm_detection(features)
            else:
                continue

            all_scores.append(scores)
            all_predictions.append(preds)

        if not all_scores:
            return np.zeros(len(features)), np.zeros(len(features))

        # 集成结果
        ensemble_scores = np.mean(all_scores, axis=0)
        ensemble_predictions = np.round(np.mean(all_predictions, axis=0)).astype(int)

        return ensemble_scores, ensemble_predictions

if __name__ == "__main__":
    # 测试代码
    detector = DynamicThreshold(window_size=50, sensitivity=2.0)
    
    # 生成测试数据（正常数据 + 异常数据）
    normal_data = np.random.normal(100, 10, 80)
    anomaly_data = np.random.normal(150, 5, 20)
    test_data = np.concatenate([normal_data, anomaly_data])
    
    # 批量检测
    results = detector.batch_detect(test_data)
    
    # 输出统计信息
    stats = detector.get_detection_stats()
    print("检测统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
        
    # 输出最近的异常检测结果
    recent_anomalies = [r for r in detector.get_recent_results(10) if r['is_anomaly']]
    print(f"\n最近检测到 {len(recent_anomalies)} 个异常")
