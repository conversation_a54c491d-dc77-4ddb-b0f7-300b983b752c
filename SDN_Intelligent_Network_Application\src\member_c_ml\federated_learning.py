#!/usr/bin/env python3
"""
联邦学习模块 - C成员
实现多控制器协同的联邦学习框架
"""

import time
import json
import threading
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import pickle
import hashlib
from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)

class FederatedRole(Enum):
    """联邦学习角色"""
    COORDINATOR = "coordinator"    # 协调者
    PARTICIPANT = "participant"   # 参与者
    AGGREGATOR = "aggregator"     # 聚合者

class ModelType(Enum):
    """模型类型"""
    RANDOM_FOREST = "random_forest"
    SVM = "svm"
    KNN = "knn"
    ENSEMBLE = "ensemble"

@dataclass
class ModelUpdate:
    """模型更新数据结构"""
    participant_id: str
    model_weights: Dict[str, Any]
    training_samples: int
    accuracy: float
    timestamp: float
    round_number: int

@dataclass
class FederatedRound:
    """联邦学习轮次"""
    round_number: int
    participants: List[str]
    start_time: float
    end_time: Optional[float]
    global_accuracy: float
    updates_received: int
    status: str  # 'running', 'completed', 'failed'

class FederatedLearningFramework:
    """联邦学习框架"""
    
    def __init__(self, node_id: str, role: FederatedRole = FederatedRole.PARTICIPANT):
        """初始化联邦学习框架"""
        self.node_id = node_id
        self.role = role
        self.is_running = False
        
        # 模型配置
        self.model_type = ModelType.ENSEMBLE
        self.local_model = None
        self.global_model = None
        self.scaler = StandardScaler()
        
        # 联邦学习配置
        self.min_participants = 2
        self.max_rounds = 100
        self.convergence_threshold = 0.001
        self.aggregation_method = 'fedavg'  # FedAvg算法
        
        # 数据和状态
        self.local_data = None
        self.training_history = []
        self.participants = {}
        self.current_round = 0
        self.model_updates = {}
        
        # 线程控制
        self.training_thread = None
        self.communication_thread = None
        
        # 统计信息
        self.stats = {
            'total_rounds': 0,
            'avg_accuracy': 0.0,
            'best_accuracy': 0.0,
            'training_time': 0.0,
            'communication_overhead': 0,
            'start_time': time.time()
        }
        
        logger.info(f"联邦学习节点 {node_id} ({role.value}) 初始化完成")
    
    def initialize_model(self, model_type: ModelType = ModelType.ENSEMBLE):
        """初始化模型"""
        self.model_type = model_type
        
        if model_type == ModelType.RANDOM_FOREST:
            self.local_model = RandomForestClassifier(
                n_estimators=50,
                max_depth=10,
                random_state=42
            )
        elif model_type == ModelType.SVM:
            self.local_model = SVC(
                kernel='rbf',
                probability=True,
                random_state=42
            )
        elif model_type == ModelType.KNN:
            self.local_model = KNeighborsClassifier(
                n_neighbors=5,
                weights='distance'
            )
        elif model_type == ModelType.ENSEMBLE:
            # 集成模型
            self.local_model = EnsembleClassifier()
        
        logger.info(f"模型初始化完成: {model_type.value}")
    
    def load_training_data(self, features: np.ndarray, labels: np.ndarray):
        """加载训练数据"""
        try:
            # 数据预处理
            features_scaled = self.scaler.fit_transform(features)
            
            # 分割训练和验证数据
            X_train, X_val, y_train, y_val = train_test_split(
                features_scaled, labels, test_size=0.2, random_state=42
            )
            
            self.local_data = {
                'X_train': X_train,
                'X_val': X_val,
                'y_train': y_train,
                'y_val': y_val,
                'feature_names': [f'feature_{i}' for i in range(features.shape[1])]
            }
            
            logger.info(f"训练数据加载完成: {X_train.shape[0]} 训练样本, {X_val.shape[0]} 验证样本")
            return True
            
        except Exception as e:
            logger.error(f"加载训练数据失败: {e}")
            return False
    
    def start_federated_learning(self):
        """启动联邦学习"""
        if self.is_running:
            logger.warning("联邦学习已在运行")
            return
        
        if self.local_data is None:
            logger.error("未加载训练数据")
            return
        
        if self.local_model is None:
            self.initialize_model()
        
        self.is_running = True
        self.stats['start_time'] = time.time()
        
        if self.role == FederatedRole.COORDINATOR:
            self.training_thread = threading.Thread(target=self._coordinator_loop, daemon=True)
        else:
            self.training_thread = threading.Thread(target=self._participant_loop, daemon=True)
        
        self.training_thread.start()
        
        # 启动通信线程
        self.communication_thread = threading.Thread(target=self._communication_loop, daemon=True)
        self.communication_thread.start()
        
        logger.info("联邦学习已启动")
    
    def stop_federated_learning(self):
        """停止联邦学习"""
        self.is_running = False
        
        if self.training_thread:
            self.training_thread.join(timeout=10)
        if self.communication_thread:
            self.communication_thread.join(timeout=5)
        
        logger.info("联邦学习已停止")
    
    def _coordinator_loop(self):
        """协调者主循环"""
        logger.info("协调者模式启动")
        
        while self.is_running and self.current_round < self.max_rounds:
            try:
                round_start_time = time.time()
                
                # 开始新一轮
                self.current_round += 1
                logger.info(f"开始第 {self.current_round} 轮联邦学习")
                
                # 等待参与者更新
                self._wait_for_participant_updates()
                
                # 聚合模型
                global_accuracy = self._aggregate_models()
                
                # 广播全局模型
                self._broadcast_global_model()
                
                # 记录轮次信息
                round_time = time.time() - round_start_time
                self.training_history.append({
                    'round': self.current_round,
                    'accuracy': global_accuracy,
                    'time': round_time,
                    'participants': len(self.participants)
                })
                
                # 检查收敛
                if self._check_convergence():
                    logger.info("模型已收敛，停止训练")
                    break
                
                time.sleep(5)  # 轮次间隔
                
            except Exception as e:
                logger.error(f"协调者循环错误: {e}")
                time.sleep(10)
        
        self._finalize_training()
    
    def _participant_loop(self):
        """参与者主循环"""
        logger.info("参与者模式启动")
        
        while self.is_running and self.current_round < self.max_rounds:
            try:
                # 等待全局模型更新
                if self._wait_for_global_model():
                    # 本地训练
                    local_accuracy = self._train_local_model()
                    
                    # 发送模型更新
                    self._send_model_update(local_accuracy)
                    
                    self.current_round += 1
                
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"参与者循环错误: {e}")
                time.sleep(10)
    
    def _train_local_model(self) -> float:
        """训练本地模型"""
        try:
            start_time = time.time()
            
            # 训练模型
            self.local_model.fit(
                self.local_data['X_train'],
                self.local_data['y_train']
            )
            
            # 验证模型
            y_pred = self.local_model.predict(self.local_data['X_val'])
            accuracy = accuracy_score(self.local_data['y_val'], y_pred)
            
            training_time = time.time() - start_time
            self.stats['training_time'] += training_time
            
            logger.info(f"本地训练完成，准确率: {accuracy:.4f}, 用时: {training_time:.2f}s")
            return accuracy
            
        except Exception as e:
            logger.error(f"本地训练失败: {e}")
            return 0.0
    
    def _wait_for_participant_updates(self):
        """等待参与者更新"""
        # 模拟等待参与者更新
        wait_time = 10  # 等待10秒
        start_time = time.time()
        
        while time.time() - start_time < wait_time:
            # 模拟接收更新
            if len(self.model_updates) >= self.min_participants:
                break
            
            # 模拟参与者发送更新
            self._simulate_participant_updates()
            time.sleep(1)
    
    def _simulate_participant_updates(self):
        """模拟参与者更新（用于演示）"""
        participant_ids = [f"participant_{i}" for i in range(1, 4)]
        
        for participant_id in participant_ids:
            if participant_id not in self.model_updates:
                # 模拟模型权重
                mock_weights = {
                    'feature_importance': np.random.random(23).tolist(),
                    'model_params': {
                        'n_estimators': 50,
                        'max_depth': 10
                    }
                }
                
                update = ModelUpdate(
                    participant_id=participant_id,
                    model_weights=mock_weights,
                    training_samples=random.randint(100, 1000),
                    accuracy=random.uniform(0.85, 0.95),
                    timestamp=time.time(),
                    round_number=self.current_round
                )
                
                self.model_updates[participant_id] = update
    
    def _aggregate_models(self) -> float:
        """聚合模型（FedAvg算法）"""
        try:
            if not self.model_updates:
                return 0.0
            
            # 计算权重（基于样本数量）
            total_samples = sum(update.training_samples for update in self.model_updates.values())
            
            # 聚合特征重要性
            aggregated_importance = np.zeros(23)
            weighted_accuracy = 0.0
            
            for update in self.model_updates.values():
                weight = update.training_samples / total_samples
                
                # 聚合特征重要性
                importance = np.array(update.model_weights['feature_importance'])
                aggregated_importance += weight * importance
                
                # 加权平均准确率
                weighted_accuracy += weight * update.accuracy
            
            # 更新全局模型
            if self.global_model is None:
                self.global_model = {
                    'feature_importance': aggregated_importance.tolist(),
                    'accuracy': weighted_accuracy,
                    'round': self.current_round
                }
            else:
                self.global_model['feature_importance'] = aggregated_importance.tolist()
                self.global_model['accuracy'] = weighted_accuracy
                self.global_model['round'] = self.current_round
            
            # 更新统计信息
            self.stats['total_rounds'] = self.current_round
            self.stats['avg_accuracy'] = weighted_accuracy
            if weighted_accuracy > self.stats['best_accuracy']:
                self.stats['best_accuracy'] = weighted_accuracy
            
            logger.info(f"模型聚合完成，全局准确率: {weighted_accuracy:.4f}")
            
            # 清空本轮更新
            self.model_updates.clear()
            
            return weighted_accuracy
            
        except Exception as e:
            logger.error(f"模型聚合失败: {e}")
            return 0.0
    
    def _broadcast_global_model(self):
        """广播全局模型"""
        # 模拟广播全局模型给所有参与者
        logger.info("广播全局模型给所有参与者")
        self.stats['communication_overhead'] += len(self.participants) * 1024  # 模拟通信开销
    
    def _wait_for_global_model(self) -> bool:
        """等待全局模型更新"""
        # 模拟等待全局模型
        if self.global_model and self.global_model['round'] > self.current_round:
            return True
        
        # 模拟接收全局模型
        if random.random() < 0.8:  # 80%概率接收到更新
            self.global_model = {
                'feature_importance': np.random.random(23).tolist(),
                'accuracy': random.uniform(0.85, 0.95),
                'round': self.current_round + 1
            }
            return True
        
        return False
    
    def _send_model_update(self, accuracy: float):
        """发送模型更新"""
        # 模拟发送模型更新给协调者
        logger.info(f"发送模型更新，准确率: {accuracy:.4f}")
        self.stats['communication_overhead'] += 1024  # 模拟通信开销
    
    def _check_convergence(self) -> bool:
        """检查模型收敛"""
        if len(self.training_history) < 3:
            return False
        
        # 检查最近3轮的准确率变化
        recent_accuracies = [h['accuracy'] for h in self.training_history[-3:]]
        accuracy_variance = np.var(recent_accuracies)
        
        return accuracy_variance < self.convergence_threshold
    
    def _communication_loop(self):
        """通信循环"""
        while self.is_running:
            try:
                # 模拟网络通信
                self._simulate_network_communication()
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"通信循环错误: {e}")
                time.sleep(10)
    
    def _simulate_network_communication(self):
        """模拟网络通信"""
        # 模拟与其他节点的通信
        if self.role == FederatedRole.COORDINATOR:
            # 协调者接收参与者状态
            for i in range(1, 4):
                participant_id = f"participant_{i}"
                self.participants[participant_id] = {
                    'status': 'active',
                    'last_seen': time.time(),
                    'accuracy': random.uniform(0.85, 0.95)
                }
        else:
            # 参与者发送心跳
            logger.debug("发送心跳给协调者")
    
    def _finalize_training(self):
        """完成训练"""
        self.is_running = False
        
        # 计算最终统计
        total_time = time.time() - self.stats['start_time']
        self.stats['training_time'] = total_time
        
        if self.training_history:
            accuracies = [h['accuracy'] for h in self.training_history]
            self.stats['avg_accuracy'] = np.mean(accuracies)
            self.stats['best_accuracy'] = np.max(accuracies)
        
        logger.info(f"联邦学习完成，总轮次: {self.current_round}, 最佳准确率: {self.stats['best_accuracy']:.4f}")
    
    def predict(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """使用全局模型进行预测"""
        try:
            if self.global_model is None:
                logger.warning("全局模型未初始化")
                return np.array([]), np.array([])
            
            # 预处理特征
            features_scaled = self.scaler.transform(features)
            
            # 使用本地模型预测（模拟全局模型）
            if self.local_model is not None:
                predictions = self.local_model.predict(features_scaled)
                probabilities = self.local_model.predict_proba(features_scaled)
                return predictions, probabilities
            
            # 简单的基于特征重要性的预测
            importance = np.array(self.global_model['feature_importance'])
            scores = np.dot(features_scaled, importance)
            predictions = (scores > np.median(scores)).astype(int)
            probabilities = np.column_stack([1 - scores, scores])
            
            return predictions, probabilities
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return np.array([]), np.array([])
    
    def get_model_performance(self) -> Dict[str, Any]:
        """获取模型性能"""
        return {
            'node_id': self.node_id,
            'role': self.role.value,
            'current_round': self.current_round,
            'stats': self.stats.copy(),
            'training_history': self.training_history.copy(),
            'global_model_accuracy': self.global_model['accuracy'] if self.global_model else 0.0,
            'participants_count': len(self.participants),
            'is_running': self.is_running
        }
    
    def export_model(self, filepath: str) -> bool:
        """导出模型"""
        try:
            model_data = {
                'global_model': self.global_model,
                'scaler': self.scaler,
                'stats': self.stats,
                'training_history': self.training_history
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"模型已导出到: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"导出模型失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.global_model = model_data['global_model']
            self.scaler = model_data['scaler']
            self.stats = model_data['stats']
            self.training_history = model_data['training_history']
            
            logger.info(f"模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return False

class EnsembleClassifier(BaseEstimator, ClassifierMixin):
    """集成分类器"""
    
    def __init__(self):
        self.classifiers = {
            'rf': RandomForestClassifier(n_estimators=50, random_state=42),
            'svm': SVC(probability=True, random_state=42),
            'knn': KNeighborsClassifier(n_neighbors=5)
        }
        self.weights = {'rf': 0.4, 'svm': 0.3, 'knn': 0.3}
    
    def fit(self, X, y):
        """训练集成模型"""
        for name, clf in self.classifiers.items():
            clf.fit(X, y)
        return self
    
    def predict(self, X):
        """集成预测"""
        predictions = {}
        for name, clf in self.classifiers.items():
            predictions[name] = clf.predict(X)
        
        # 加权投票
        final_predictions = []
        for i in range(len(X)):
            votes = {}
            for name, pred in predictions.items():
                vote = pred[i]
                if vote not in votes:
                    votes[vote] = 0
                votes[vote] += self.weights[name]
            
            final_predictions.append(max(votes, key=votes.get))
        
        return np.array(final_predictions)
    
    def predict_proba(self, X):
        """集成概率预测"""
        probas = {}
        for name, clf in self.classifiers.items():
            probas[name] = clf.predict_proba(X)
        
        # 加权平均概率
        final_probas = np.zeros_like(probas[list(probas.keys())[0]])
        for name, proba in probas.items():
            final_probas += self.weights[name] * proba
        
        return final_probas

if __name__ == "__main__":
    import random

    # 测试联邦学习
    coordinator = FederatedLearningFramework("coordinator_1", FederatedRole.COORDINATOR)

    # 生成模拟数据
    X = np.random.random((1000, 23))
    y = np.random.randint(0, 2, 1000)

    coordinator.load_training_data(X, y)
    coordinator.start_federated_learning()

    try:
        time.sleep(30)
        performance = coordinator.get_model_performance()
        print(f"联邦学习性能: {json.dumps(performance, indent=2, default=str)}")

    finally:
        coordinator.stop_federated_learning()
