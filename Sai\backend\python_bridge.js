/**
 * Python桥接模块
 * 用于调用B成员的Python流量检测模块
 */

const { spawn } = require('child_process');
const path = require('path');

class PythonBridge {
    constructor() {
        this.pythonPath = 'python'; // 可配置Python路径
        this.srcPath = path.join(__dirname, '../../src');
        this.trafficDetectionPath = path.join(this.srcPath, 'traffic_detection');
    }

    /**
     * 执行Python脚本并返回结果
     */
    async executePythonScript(scriptPath, args = []) {
        return new Promise((resolve, reject) => {
            const python = spawn(this.pythonPath, [scriptPath, ...args], {
                cwd: this.srcPath
            });

            let stdout = '';
            let stderr = '';

            python.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            python.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            python.on('close', (code) => {
                if (code === 0) {
                    try {
                        // 尝试解析JSON输出
                        const result = JSON.parse(stdout);
                        resolve(result);
                    } catch (e) {
                        // 如果不是JSON，返回原始文本
                        resolve({ output: stdout.trim() });
                    }
                } else {
                    reject(new Error(`Python脚本执行失败: ${stderr}`));
                }
            });

            python.on('error', (error) => {
                reject(new Error(`启动Python进程失败: ${error.message}`));
            });
        });
    }

    /**
     * 获取实时流量统计
     */
    async getRealTimeStats() {
        try {
            const scriptPath = path.join(this.trafficDetectionPath, 'api_bridge.py');
            const result = await this.executePythonScript(scriptPath, ['realtime_stats']);
            return result;
        } catch (error) {
            console.error('获取实时统计失败:', error);
            return {
                packets_per_second: 0,
                flows_per_second: 0,
                bytes_per_second: 0,
                anomaly_rate: 0,
                current_threshold: 2.0,
                total_flows: 0
            };
        }
    }

    /**
     * 获取检测结果
     */
    async getDetectionResults(limit = 10) {
        try {
            const scriptPath = path.join(this.trafficDetectionPath, 'api_bridge.py');
            const result = await this.executePythonScript(scriptPath, ['detection_results', limit.toString()]);
            return result;
        } catch (error) {
            console.error('获取检测结果失败:', error);
            return { recent_detections: [], anomaly_summary: {} };
        }
    }

    /**
     * 获取流量特征数据
     */
    async getTrafficFeatures(flowId = null) {
        try {
            const scriptPath = path.join(this.trafficDetectionPath, 'api_bridge.py');
            const args = ['traffic_features'];
            if (flowId) args.push(flowId);
            
            const result = await this.executePythonScript(scriptPath, args);
            return result;
        } catch (error) {
            console.error('获取流量特征失败:', error);
            return { features: [], feature_names: [] };
        }
    }

    /**
     * 触发异常检测
     */
    async triggerAnomalyDetection() {
        try {
            const scriptPath = path.join(this.trafficDetectionPath, 'api_bridge.py');
            const result = await this.executePythonScript(scriptPath, ['trigger_detection']);
            return result;
        } catch (error) {
            console.error('触发异常检测失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成演示数据
     */
    async generateDemoData() {
        try {
            const scriptPath = path.join(this.trafficDetectionPath, 'api_bridge.py');
            const result = await this.executePythonScript(scriptPath, ['generate_demo']);
            return result;
        } catch (error) {
            console.error('生成演示数据失败:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取性能指标
     */
    async getPerformanceMetrics() {
        try {
            const scriptPath = path.join(this.trafficDetectionPath, 'api_bridge.py');
            const result = await this.executePythonScript(scriptPath, ['performance_metrics']);
            return result;
        } catch (error) {
            console.error('获取性能指标失败:', error);
            return {
                processing_speed: 0,
                detection_accuracy: 0,
                memory_usage: 0,
                uptime: 0
            };
        }
    }
}

module.exports = PythonBridge;
