#!/usr/bin/env python3
"""
系统启动脚本
一键启动整个SDN智能网络应用系统
"""

import os
import sys
import time
import subprocess
import threading
import signal
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / 'src'))

from shared.config import SYSTEM_CONFIG, get_log_file_path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(get_log_file_path('system_startup')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.processes = {}
        self.is_running = False
        self.project_root = project_root
        
    def start_system(self):
        """启动整个系统"""
        logger.info("🚀 开始启动SDN智能网络应用系统")
        
        try:
            # 检查环境
            self._check_environment()
            
            # 启动各个模块
            self._start_modules()
            
            # 等待所有模块启动
            self._wait_for_startup()
            
            # 显示系统信息
            self._show_system_info()
            
            self.is_running = True
            logger.info("✅ 系统启动完成！")
            
            # 保持运行
            self._keep_running()
            
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭系统...")
            self.stop_system()
        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            self.stop_system()
            sys.exit(1)
    
    def _check_environment(self):
        """检查运行环境"""
        logger.info("🔍 检查运行环境...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            raise RuntimeError("需要Python 3.8或更高版本")
        
        # 检查必要的包
        required_packages = ['numpy', 'pandas', 'sklearn', 'flask']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少必要的包: {missing_packages}")
            logger.info("请运行: pip install -r requirements.txt")
            raise RuntimeError("缺少必要的依赖包")
        
        # 检查端口可用性
        self._check_ports()
        
        logger.info("✅ 环境检查通过")
    
    def _check_ports(self):
        """检查端口可用性"""
        import socket
        
        ports_to_check = [
            SYSTEM_CONFIG['sdn_controller']['port'],
            SYSTEM_CONFIG['frontend']['backend_port'],
            SYSTEM_CONFIG['frontend']['frontend_port'],
            SYSTEM_CONFIG['frontend']['websocket_port']
        ]
        
        for port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            try:
                result = sock.connect_ex(('127.0.0.1', port))
                if result == 0:
                    logger.warning(f"端口 {port} 已被占用")
                sock.close()
            except Exception:
                pass
    
    def _start_modules(self):
        """启动各个模块"""
        logger.info("📦 启动系统模块...")
        
        # 模块启动顺序很重要
        modules = [
            {
                'name': 'A成员-SDN控制器',
                'script': 'src/member_a_architecture/sdn_controller.py',
                'wait_time': 3
            },
            {
                'name': 'B成员-流量检测',
                'script': 'src/member_b_detection/enhanced_traffic_collector.py',
                'wait_time': 2
            },
            {
                'name': 'C成员-机器学习',
                'script': 'src/member_c_ml/ml_detector.py',
                'wait_time': 2
            },
            {
                'name': 'D成员-后端API',
                'script': 'src/member_d_frontend/backend/app.py',
                'wait_time': 3
            }
        ]
        
        for module in modules:
            self._start_module(module)
    
    def _start_module(self, module_config):
        """启动单个模块"""
        name = module_config['name']
        script_path = self.project_root / module_config['script']
        wait_time = module_config['wait_time']
        
        if not script_path.exists():
            logger.warning(f"模块脚本不存在: {script_path}")
            return
        
        logger.info(f"启动 {name}...")
        
        try:
            # 启动进程
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[name] = process
            logger.info(f"✅ {name} 启动成功 (PID: {process.pid})")
            
            # 等待模块初始化
            time.sleep(wait_time)
            
        except Exception as e:
            logger.error(f"❌ {name} 启动失败: {e}")
    
    def _wait_for_startup(self):
        """等待所有模块启动完成"""
        logger.info("⏳ 等待所有模块启动完成...")
        
        max_wait_time = 30  # 最大等待30秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # 检查所有进程是否还在运行
            all_running = True
            for name, process in self.processes.items():
                if process.poll() is not None:
                    logger.error(f"模块 {name} 意外退出")
                    all_running = False
            
            if all_running and len(self.processes) > 0:
                break
            
            time.sleep(1)
        
        # 额外等待时间让模块完全初始化
        time.sleep(5)
    
    def _show_system_info(self):
        """显示系统信息"""
        print("\n" + "="*60)
        print("🎉 SDN智能网络应用系统启动成功！")
        print("="*60)
        print(f"📍 主界面: http://localhost:{SYSTEM_CONFIG['frontend']['frontend_port']}")
        print(f"🔧 API接口: http://localhost:{SYSTEM_CONFIG['frontend']['backend_port']}/api")
        print(f"📊 监控面板: http://localhost:{SYSTEM_CONFIG['frontend']['frontend_port']}/dashboard")
        print("="*60)
        print("🎮 功能演示:")
        print("  - 访问主界面查看实时监控")
        print("  - 点击'触发检测'按钮测试检测功能")
        print("  - 点击'生成演示数据'按钮生成测试数据")
        print("  - 查看实时流量图表和异常告警")
        print("="*60)
        print("📋 运行状态:")
        for name, process in self.processes.items():
            status = "运行中" if process.poll() is None else "已停止"
            print(f"  {name}: {status} (PID: {process.pid})")
        print("="*60)
        print("💡 提示: 按 Ctrl+C 停止系统")
        print()
    
    def _keep_running(self):
        """保持系统运行"""
        try:
            while self.is_running:
                # 检查进程状态
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.warning(f"模块 {name} 意外退出，尝试重启...")
                        # 这里可以添加重启逻辑
                
                time.sleep(5)
                
        except KeyboardInterrupt:
            pass
    
    def stop_system(self):
        """停止系统"""
        logger.info("🛑 正在停止系统...")
        self.is_running = False
        
        # 停止所有进程
        for name, process in self.processes.items():
            if process.poll() is None:
                logger.info(f"停止 {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning(f"强制终止 {name}...")
                    process.kill()
                except Exception as e:
                    logger.error(f"停止 {name} 失败: {e}")
        
        logger.info("✅ 系统已停止")

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info("收到停止信号")
    if 'manager' in globals():
        manager.stop_system()
    sys.exit(0)

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建系统管理器
    global manager
    manager = SystemManager()
    
    # 启动系统
    manager.start_system()

if __name__ == "__main__":
    main()
