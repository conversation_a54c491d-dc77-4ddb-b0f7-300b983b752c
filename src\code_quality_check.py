#!/usr/bin/env python3
"""
代码质量检查脚本
Code Quality Check Script

功能：
- 检查代码规范性
- 统计代码质量指标
- 验证模块完整性
- 生成质量报告
"""

import os
import ast
import sys
from pathlib import Path
import json
from datetime import datetime

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_path):
        """初始化检查器"""
        self.project_path = Path(project_path)
        self.traffic_detection_path = self.project_path / "traffic_detection"
        self.quality_report = {
            'timestamp': datetime.now().isoformat(),
            'project_path': str(self.project_path),
            'modules': {},
            'summary': {},
            'issues': [],
            'recommendations': []
        }
        
    def analyze_python_file(self, file_path):
        """分析Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析AST
            tree = ast.parse(content)
            
            # 统计信息
            stats = {
                'file_path': str(file_path),
                'total_lines': len(content.splitlines()),
                'code_lines': 0,
                'comment_lines': 0,
                'blank_lines': 0,
                'functions': 0,
                'classes': 0,
                'imports': 0,
                'docstrings': 0,
                'has_main': False
            }
            
            # 分析代码行
            lines = content.splitlines()
            for line in lines:
                stripped = line.strip()
                if not stripped:
                    stats['blank_lines'] += 1
                elif stripped.startswith('#'):
                    stats['comment_lines'] += 1
                else:
                    stats['code_lines'] += 1
                    
            # 分析AST节点
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    stats['functions'] += 1
                    # 检查是否有文档字符串
                    if (node.body and isinstance(node.body[0], ast.Expr) and
                        isinstance(node.body[0].value, ast.Str)):
                        stats['docstrings'] += 1
                elif isinstance(node, ast.ClassDef):
                    stats['classes'] += 1
                elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    stats['imports'] += 1
                    
            # 检查是否有main函数
            if 'if __name__ == "__main__"' in content:
                stats['has_main'] = True
                
            # 计算质量指标
            if stats['total_lines'] > 0:
                stats['comment_ratio'] = stats['comment_lines'] / stats['total_lines']
            else:
                stats['comment_ratio'] = 0
                
            if stats['functions'] > 0:
                stats['docstring_ratio'] = stats['docstrings'] / stats['functions']
            else:
                stats['docstring_ratio'] = 0
                
            return stats
            
        except Exception as e:
            self.quality_report['issues'].append(f"分析文件 {file_path} 时出错: {e}")
            return None
            
    def check_module_completeness(self):
        """检查模块完整性"""
        required_files = [
            '__init__.py',
            'traffic_collector.py',
            'feature_extractor.py', 
            'dynamic_threshold.py',
            'statistical_analyzer.py',
            'test_modules.py',
            'comprehensive_demo.py',
            'performance_test.py'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_name in required_files:
            file_path = self.traffic_detection_path / file_name
            if file_path.exists():
                existing_files.append(file_name)
            else:
                missing_files.append(file_name)
                
        return existing_files, missing_files
        
    def check_dependencies(self):
        """检查依赖文件"""
        requirements_file = self.project_path / "requirements.txt"
        readme_file = self.project_path / "README.md"
        
        deps_status = {
            'requirements_exists': requirements_file.exists(),
            'readme_exists': readme_file.exists(),
            'requirements_size': requirements_file.stat().st_size if requirements_file.exists() else 0,
            'readme_size': readme_file.stat().st_size if readme_file.exists() else 0
        }
        
        return deps_status
        
    def analyze_all_modules(self):
        """分析所有模块"""
        if not self.traffic_detection_path.exists():
            self.quality_report['issues'].append("traffic_detection目录不存在")
            return
            
        python_files = list(self.traffic_detection_path.glob("*.py"))
        
        total_stats = {
            'total_files': len(python_files),
            'total_lines': 0,
            'total_functions': 0,
            'total_classes': 0,
            'avg_comment_ratio': 0,
            'avg_docstring_ratio': 0
        }
        
        comment_ratios = []
        docstring_ratios = []
        
        for py_file in python_files:
            stats = self.analyze_python_file(py_file)
            if stats:
                self.quality_report['modules'][py_file.name] = stats
                total_stats['total_lines'] += stats['total_lines']
                total_stats['total_functions'] += stats['functions']
                total_stats['total_classes'] += stats['classes']
                comment_ratios.append(stats['comment_ratio'])
                docstring_ratios.append(stats['docstring_ratio'])
                
        if comment_ratios:
            total_stats['avg_comment_ratio'] = sum(comment_ratios) / len(comment_ratios)
        if docstring_ratios:
            total_stats['avg_docstring_ratio'] = sum(docstring_ratios) / len(docstring_ratios)
            
        return total_stats
        
    def generate_recommendations(self, total_stats):
        """生成改进建议"""
        recommendations = []
        
        # 注释率检查
        if total_stats['avg_comment_ratio'] < 0.15:
            recommendations.append("建议增加代码注释，目标注释率 >15%")
        elif total_stats['avg_comment_ratio'] > 0.30:
            recommendations.append("注释率很好，保持当前水平")
        else:
            recommendations.append("注释率良好")
            
        # 文档字符串检查
        if total_stats['avg_docstring_ratio'] < 0.80:
            recommendations.append("建议为更多函数添加文档字符串，目标覆盖率 >80%")
        else:
            recommendations.append("文档字符串覆盖率优秀")
            
        # 代码规模检查
        if total_stats['total_lines'] > 5000:
            recommendations.append("代码规模较大，建议考虑进一步模块化")
        elif total_stats['total_lines'] < 1000:
            recommendations.append("代码规模较小，可以考虑增加更多功能")
        else:
            recommendations.append("代码规模适中")
            
        return recommendations
        
    def run_quality_check(self):
        """运行完整的质量检查"""
        print("🔍 开始代码质量检查...")
        
        # 1. 检查模块完整性
        print("1. 检查模块完整性...")
        existing_files, missing_files = self.check_module_completeness()
        
        self.quality_report['summary']['existing_files'] = existing_files
        self.quality_report['summary']['missing_files'] = missing_files
        
        if missing_files:
            self.quality_report['issues'].append(f"缺少文件: {missing_files}")
        
        print(f"   ✅ 存在文件: {len(existing_files)}")
        print(f"   ⚠️  缺失文件: {len(missing_files)}")
        
        # 2. 检查依赖文件
        print("2. 检查依赖文件...")
        deps_status = self.check_dependencies()
        self.quality_report['summary']['dependencies'] = deps_status
        
        if deps_status['requirements_exists']:
            print("   ✅ requirements.txt 存在")
        else:
            print("   ❌ requirements.txt 缺失")
            
        if deps_status['readme_exists']:
            print("   ✅ README.md 存在")
        else:
            print("   ❌ README.md 缺失")
            
        # 3. 分析代码质量
        print("3. 分析代码质量...")
        total_stats = self.analyze_all_modules()
        
        if total_stats:
            self.quality_report['summary']['code_stats'] = total_stats
            
            print(f"   📊 总文件数: {total_stats['total_files']}")
            print(f"   📊 总行数: {total_stats['total_lines']}")
            print(f"   📊 总函数数: {total_stats['total_functions']}")
            print(f"   📊 总类数: {total_stats['total_classes']}")
            print(f"   📊 平均注释率: {total_stats['avg_comment_ratio']:.1%}")
            print(f"   📊 平均文档字符串覆盖率: {total_stats['avg_docstring_ratio']:.1%}")
            
            # 4. 生成建议
            print("4. 生成改进建议...")
            recommendations = self.generate_recommendations(total_stats)
            self.quality_report['recommendations'] = recommendations
            
            for rec in recommendations:
                print(f"   💡 {rec}")
                
        # 5. 计算总体评分
        score = self.calculate_overall_score()
        self.quality_report['summary']['overall_score'] = score
        
        print(f"\n🎯 总体质量评分: {score}/100")
        
        return self.quality_report
        
    def calculate_overall_score(self):
        """计算总体评分"""
        score = 100
        
        # 文件完整性 (30分)
        existing_files = len(self.quality_report['summary'].get('existing_files', []))
        total_required = 8  # 8个必需文件
        file_score = (existing_files / total_required) * 30
        
        # 代码质量 (40分)
        code_stats = self.quality_report['summary'].get('code_stats', {})
        comment_ratio = code_stats.get('avg_comment_ratio', 0)
        docstring_ratio = code_stats.get('avg_docstring_ratio', 0)
        
        comment_score = min(comment_ratio / 0.15, 1.0) * 20  # 注释率目标15%
        docstring_score = min(docstring_ratio / 0.80, 1.0) * 20  # 文档字符串目标80%
        
        # 依赖文件 (20分)
        deps = self.quality_report['summary'].get('dependencies', {})
        deps_score = 0
        if deps.get('requirements_exists'):
            deps_score += 10
        if deps.get('readme_exists'):
            deps_score += 10
            
        # 问题扣分 (10分)
        issues_count = len(self.quality_report.get('issues', []))
        issue_penalty = min(issues_count * 2, 10)  # 每个问题扣2分，最多扣10分
        
        final_score = file_score + comment_score + docstring_score + deps_score - issue_penalty
        return max(0, min(100, int(final_score)))
        
    def save_report(self, filename="code_quality_report.json"):
        """保存质量报告"""
        report_path = self.project_path / filename
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.quality_report, f, indent=2, ensure_ascii=False)
        print(f"\n📄 质量报告已保存到: {report_path}")

def main():
    """主函数"""
    project_path = Path(__file__).parent
    
    checker = CodeQualityChecker(project_path)
    report = checker.run_quality_check()
    checker.save_report()
    
    # 输出总结
    score = report['summary']['overall_score']
    if score >= 90:
        print("🏆 代码质量优秀！")
    elif score >= 80:
        print("✅ 代码质量良好！")
    elif score >= 70:
        print("⚠️  代码质量一般，建议改进")
    else:
        print("❌ 代码质量需要改进")

if __name__ == "__main__":
    main()
