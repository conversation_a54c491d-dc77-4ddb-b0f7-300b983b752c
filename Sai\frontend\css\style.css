body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header {
    background-color: #4a6797;
    color: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

main {
    display: flex;
    flex-grow: 1;
    padding: 20px;
    gap: 20px;
}

.left-panel, .right-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.left-panel {
    flex: 1; /* 左侧占据一半宽度 */
    min-width: 400px;
}

.right-panel {
    flex: 2; /* 右侧占据两倍宽度，内容更多 */
    min-width: 600px;
}

.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    flex-grow: 1; /* 让卡片填充可用空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出卡片 */
}

.card h2 {
    margin-top: 0;
    color: #4a6797;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Specific card styles */
.network-topology #topology-container {
    background-color: #f9f9f9;
    border: 1px dashed #ccc;
    height: 250px; /* 固定高度 */
    display: flex;
    justify-content: center;
    align-items: center;
    font-style: italic;
    color: #666;
    overflow-y: auto; /* For scrollable text content */
}

.flow-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

.flow-table th, .flow-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.flow-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.flow-table tbody {
    max-height: 300px; /* 限制流表体的高度并添加滚动条 */
    overflow-y: auto;
    display: block; /* 使 tbody 可以设置高度和滚动 */
    width: 100%;
}

.flow-table tr {
    display: table; /* 使行显示为表格行，配合 tbody display: block */
    width: 100%;
    table-layout: fixed; /* 固定列宽 */
}

.traffic-trend #traffic-chart {
    min-height: 300px; /* ECharts 容器需要固定高度 */
}

.alert-log #alert-log-container {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    padding: 10px;
    max-height: 250px; /* 限制告警日志高度 */
    overflow-y: auto; /* 允许滚动 */
}

.alert-log #alert-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.alert-log #alert-list li {
    background-color: #ffe0e0; /* 默认告警背景 */
    border-left: 5px solid #d9534f;
    padding: 8px 10px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-size: 0.85em;
    word-wrap: break-word; /* 避免长文本溢出 */
}

.alert-log #alert-list li.severity-High {
    background-color: #fdd;
    border-color: #d9534f;
}

.alert-log #alert-list li.severity-Medium {
    background-color: #fff8e1;
    border-color: #f0ad4e;
}

footer {
    text-align: center;
    padding: 15px;
    background-color: #eee;
    color: #666;
    font-size: 0.9em;
    margin-top: auto; /* 将 footer 推到底部 */
}