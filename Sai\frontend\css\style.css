body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header {
    background-color: #4a6797;
    color: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

main {
    display: flex;
    flex-grow: 1;
    padding: 20px;
    gap: 20px;
}

.left-panel, .right-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.left-panel {
    flex: 1; /* 左侧占据一半宽度 */
    min-width: 400px;
}

.right-panel {
    flex: 2; /* 右侧占据两倍宽度，内容更多 */
    min-width: 600px;
}

.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    flex-grow: 1; /* 让卡片填充可用空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出卡片 */
}

.card h2 {
    margin-top: 0;
    color: #4a6797;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Specific card styles */
.network-topology #topology-container {
    background-color: #f9f9f9;
    border: 1px dashed #ccc;
    height: 250px; /* 固定高度 */
    display: flex;
    justify-content: center;
    align-items: center;
    font-style: italic;
    color: #666;
    overflow-y: auto; /* For scrollable text content */
}

.flow-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}

.flow-table th, .flow-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.flow-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.flow-table tbody {
    max-height: 300px; /* 限制流表体的高度并添加滚动条 */
    overflow-y: auto;
    display: block; /* 使 tbody 可以设置高度和滚动 */
    width: 100%;
}

.flow-table tr {
    display: table; /* 使行显示为表格行，配合 tbody display: block */
    width: 100%;
    table-layout: fixed; /* 固定列宽 */
}

.traffic-trend #traffic-chart {
    min-height: 300px; /* ECharts 容器需要固定高度 */
}

.alert-log #alert-log-container {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    padding: 10px;
    max-height: 250px; /* 限制告警日志高度 */
    overflow-y: auto; /* 允许滚动 */
}

.alert-log #alert-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.alert-log #alert-list li {
    background-color: #ffe0e0; /* 默认告警背景 */
    border-left: 5px solid #d9534f;
    padding: 8px 10px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-size: 0.85em;
    word-wrap: break-word; /* 避免长文本溢出 */
}

.alert-log #alert-list li.severity-High {
    background-color: #fdd;
    border-color: #d9534f;
}

.alert-log #alert-list li.severity-Medium {
    background-color: #fff8e1;
    border-color: #f0ad4e;
}

footer {
    text-align: center;
    padding: 15px;
    background-color: #eee;
    color: #666;
    font-size: 0.9em;
    margin-top: auto; /* 将 footer 推到底部 */
}

/* === B成员AI检测模块样式 === */

/* AI检测告警样式 */
.ai-detection {
    background: linear-gradient(90deg, #e3f2fd, #f3e5f5);
    border-left: 4px solid #2196f3;
}

/* B成员统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.stat-label {
    font-weight: 500;
    color: #495057;
}

.stat-value {
    font-weight: bold;
    color: #007bff;
}

.stat-value.alert {
    color: #dc3545;
    animation: pulse 1s infinite;
}

/* B成员性能指标网格 */
.metrics-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 10px;
    background: #e8f5e8;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.metric-label {
    font-weight: 500;
    color: #495057;
}

.metric-value {
    font-weight: bold;
    color: #28a745;
}

/* B成员控制按钮 */
.control-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}

/* B成员检测结果样式 */
#detection-results ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#detection-results li {
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#detection-results li.normal {
    background: #d4edda;
    border-left: 3px solid #28a745;
}

#detection-results li.anomaly {
    background: #f8d7da;
    border-left: 3px solid #dc3545;
    animation: pulse 2s infinite;
}

.time {
    font-size: 0.9em;
    color: #6c757d;
    min-width: 80px;
}

.value, .score {
    font-size: 0.9em;
    font-weight: 500;
}

.alert-badge {
    background: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* AI检测卡片特殊样式 */
.ai-detection-stats {
    border-top: 3px solid #2196f3;
}

.ai-detection-stats h2 {
    color: #1976d2;
}

.detection-results {
    border-top: 3px solid #ff9800;
}

.detection-results h2 {
    color: #f57c00;
}

.performance-metrics {
    border-top: 3px solid #4caf50;
}

.performance-metrics h2 {
    color: #388e3c;
}