@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 SDN智能网络应用 - 集成系统启动
echo ========================================
echo.

:: 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)
echo ✅ Python环境正常

:: 检查Node.js环境
echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或不在PATH中
    echo 请先安装Node.js 14+
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

:: 检查B成员Python模块
echo 🔍 检查B成员Python模块...
if not exist "..\src\traffic_detection\api_bridge.py" (
    echo ❌ 未找到B成员的API桥接脚本
    echo 请确保B成员的代码已正确部署
    pause
    exit /b 1
)
echo ✅ B成员模块存在

:: 安装Python依赖
echo 📦 安装Python依赖...
cd ..\src
python -m pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Python依赖安装可能有问题，但继续启动...
) else (
    echo ✅ Python依赖安装完成
)

:: 安装Node.js依赖
echo 📦 安装Node.js依赖...
cd ..\Sai\backend
npm install >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js依赖安装失败
    pause
    exit /b 1
)
echo ✅ Node.js依赖安装完成

:: 启动后端服务器
echo 🚀 启动后端服务器...
echo.
echo ========================================
echo 🌐 服务器启动中...
echo 📍 前端地址: http://localhost:3000
echo 🔧 后端API: http://localhost:3000/api
echo 🤖 B成员AI检测: 已集成
echo ========================================
echo.
echo 💡 提示：
echo   - 浏览器将自动打开前端界面
echo   - 可以使用"触发检测"按钮测试B成员功能
echo   - 可以使用"生成演示数据"按钮生成测试数据
echo   - 按 Ctrl+C 停止服务器
echo.

:: 等待2秒后自动打开浏览器
timeout /t 2 /nobreak >nul
start http://localhost:3000

:: 启动Node.js服务器
npm start
