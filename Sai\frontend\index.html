<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDN 威胁检测可视化平台</title>
    <link rel="stylesheet" href="./css/style.css">
    <!-- 引入 ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
</head>
<body>
    <header>
        <h1>SDN 威胁检测可视化平台</h1>
    </header>
    <main>
        <section class="left-panel">
            <div class="card network-topology">
                <h2>网络拓扑</h2>
                <div id="topology-container">
                    <p>加载中...</p>
                </div>
            </div>
            <div class="card flow-table">
                <h2>流表信息</h2>
                <div id="flow-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>DPID</th>
                                <th>匹配规则</th>
                                <th>动作</th>
                                <th>包数</th>
                                <th>字节数</th>
                            </tr>
                        </thead>
                        <tbody id="flow-table-body">
                            <tr><td colspan="6">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <section class="right-panel">
            <!-- B成员AI检测统计 -->
            <div class="card ai-detection-stats">
                <h2>🤖 AI流量检测统计</h2>
                <div id="bmember-stats" class="stats-grid">
                    <p>加载中...</p>
                </div>
                <div class="control-buttons">
                    <button id="trigger-detection-btn" class="btn btn-primary">触发检测</button>
                    <button id="generate-demo-btn" class="btn btn-secondary">生成演示数据</button>
                </div>
            </div>

            <div class="card traffic-trend">
                <h2>流量趋势</h2>
                <div id="traffic-chart" style="width: 100%; height: 300px;"></div>
            </div>

            <!-- B成员检测结果 -->
            <div class="card detection-results">
                <h2>🔍 异常检测结果</h2>
                <div id="detection-results">
                    <p>加载中...</p>
                </div>
            </div>

            <!-- B成员性能指标 -->
            <div class="card performance-metrics">
                <h2>📊 性能指标</h2>
                <div id="performance-metrics" class="metrics-grid">
                    <p>加载中...</p>
                </div>
            </div>

            <div class="card alert-log">
                <h2>🚨 告警信息</h2>
                <div id="alert-log-container">
                    <ul id="alert-list">
                        <li>等待告警信息...</li>
                    </ul>
                </div>
            </div>
        </section>
    </main>
    <footer>
        <p>&copy; 2024 SDN Security Team - 队员 D</p>
    </footer>

    <script src="./js/main.js"></script>
</body>
</html>