const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const PythonBridge = require('./python_bridge');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// 初始化Python桥接器
const pythonBridge = new PythonBridge();

const PORT = 3000;

// --- 静态文件服务 ---
// 将 frontend 文件夹设置为静态资源目录
app.use(express.static(path.join(__dirname, '../frontend')));

// --- API 数据模拟 ---

// 模拟网络拓扑数据
let topologyData = {
    nodes: [
        { id: 'h1', name: 'Host 1', type: 'host', ip: '********' },
        { id: 'h2', name: 'Host 2', type: 'host', ip: '********' },
        { id: 's1', name: 'Switch 1', type: 'switch', dpId: '00:00:00:00:00:00:00:01' },
        { id: 's2', name: 'Switch 2', type: 'switch', dpId: '00:00:00:00:00:00:00:02' }
    ],
    links: [
        { source: 'h1', target: 's1' },
        { source: 'h2', target: 's2' },
        { source: 's1', target: 's2' }
    ]
};

// 模拟流表数据
let flowTableData = [
    { id: 1, dpId: '00:00:00:00:00:00:00:01', match: 'in_port=1,eth_type=0x800', action: 'output:2', packetCount: 12345, byteCount: 98765432 },
    { id: 2, dpId: '00:00:00:00:00:00:00:01', match: 'in_port=2,eth_type=0x800', action: 'output:1', packetCount: 11223, byteCount: 88776655 },
    { id: 3, dpId: '00:00:00:00:00:00:00:02', match: 'in_port=1,eth_type=0x800', action: 'output:2', packetCount: 9876, byteCount: 76543210 }
];

// 模拟实时流量数据 (用于 WebSocket 推送)
let currentTraffic = 0;
let trafficHistory = []; // 存储历史数据点
const MAX_HISTORY_POINTS = 100; // 历史数据点数量

function generateTrafficData() {
    currentTraffic += (Math.random() - 0.5) * 20 + 50; // 模拟波动流量，平均50
    if (currentTraffic < 0) currentTraffic = 0;
    const now = new Date();
    trafficHistory.push({ time: now.toLocaleTimeString(), value: currentTraffic });
    if (trafficHistory.length > MAX_HISTORY_POINTS) {
        trafficHistory.shift(); // 移除最旧的数据点
    }
    return {
        timestamp: now.toISOString(),
        bandwidth: currentTraffic,
        packetRate: Math.floor(currentTraffic * 1.2 * (0.8 + Math.random() * 0.4)) // 模拟包速率
    };
}

// 模拟告警数据 (用于 WebSocket 推送)
const attackTypes = ['DDoS', 'Port Scan', 'SYN Flood', 'Brute Force'];
const sourceIPs = ['*************', '********', '***********', '*************', '********'];
const destinationIPs = ['********', '********', '********'];

function generateAlertData() {
    const isAttack = Math.random() < 0.3; // 30%的概率生成告警
    if (isAttack) {
        return {
            timestamp: new Date().toISOString(),
            type: attackTypes[Math.floor(Math.random() * attackTypes.length)],
            source: sourceIPs[Math.floor(Math.random() * sourceIPs.length)],
            destination: destinationIPs[Math.floor(Math.random() * destinationIPs.length)],
            severity: Math.random() < 0.6 ? 'High' : 'Medium',
            description: 'Suspicious activity detected.'
        };
    }
    return null; // 不总是生成告警
}

// --- RESTful API 路由 ---
app.get('/api/topology', (req, res) => {
    res.json(topologyData);
});

app.get('/api/flowtables', (req, res) => {
    res.json(flowTableData);
});

// --- B成员流量检测模块API ---

// 获取实时流量统计
app.get('/api/realtime-stats', async (req, res) => {
    try {
        const stats = await pythonBridge.getRealTimeStats();
        res.json(stats);
    } catch (error) {
        console.error('获取实时统计失败:', error);
        res.status(500).json({ error: '获取实时统计失败' });
    }
});

// 获取检测结果
app.get('/api/detection-results', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 10;
        const results = await pythonBridge.getDetectionResults(limit);
        res.json(results);
    } catch (error) {
        console.error('获取检测结果失败:', error);
        res.status(500).json({ error: '获取检测结果失败' });
    }
});

// 获取流量特征
app.get('/api/traffic-features', async (req, res) => {
    try {
        const flowId = req.query.flowId || null;
        const features = await pythonBridge.getTrafficFeatures(flowId);
        res.json(features);
    } catch (error) {
        console.error('获取流量特征失败:', error);
        res.status(500).json({ error: '获取流量特征失败' });
    }
});

// 触发异常检测
app.post('/api/trigger-detection', async (req, res) => {
    try {
        const result = await pythonBridge.triggerAnomalyDetection();
        res.json(result);
    } catch (error) {
        console.error('触发异常检测失败:', error);
        res.status(500).json({ error: '触发异常检测失败' });
    }
});

// 生成演示数据
app.post('/api/generate-demo', async (req, res) => {
    try {
        const result = await pythonBridge.generateDemoData();
        res.json(result);
    } catch (error) {
        console.error('生成演示数据失败:', error);
        res.status(500).json({ error: '生成演示数据失败' });
    }
});

// 获取性能指标
app.get('/api/performance-metrics', async (req, res) => {
    try {
        const metrics = await pythonBridge.getPerformanceMetrics();
        res.json(metrics);
    } catch (error) {
        console.error('获取性能指标失败:', error);
        res.status(500).json({ error: '获取性能指标失败' });
    }
});

// --- WebSocket 服务 ---
wss.on('connection', ws => {
    console.log('Client connected via WebSocket');

    // 初始发送部分历史流量数据，让图表不为空
    ws.send(JSON.stringify({ type: 'trafficHistory', data: trafficHistory }));

    ws.on('close', () => {
        console.log('Client disconnected from WebSocket');
    });

    ws.on('error', error => {
        console.error('WebSocket error:', error);
    });
});

// 每秒推送实时数据到所有连接的客户端
setInterval(async () => {
    try {
        // 获取B成员的实时统计数据
        const realTimeStats = await pythonBridge.getRealTimeStats();
        const detectionResults = await pythonBridge.getDetectionResults(5);

        // 生成模拟流量数据（保持原有功能）
        const traffic = generateTrafficData();
        const alert = generateAlertData();

        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                // 推送B成员的实时统计数据
                client.send(JSON.stringify({
                    type: 'realtime-stats',
                    data: realTimeStats
                }));

                // 推送B成员的检测结果
                if (detectionResults.recent_detections && detectionResults.recent_detections.length > 0) {
                    client.send(JSON.stringify({
                        type: 'detection-results',
                        data: detectionResults
                    }));
                }

                // 推送原有的流量数据（保持兼容性）
                client.send(JSON.stringify({ type: 'traffic', data: traffic }));

                // 推送告警数据 (如果有)
                if (alert) {
                    client.send(JSON.stringify({ type: 'alert', data: alert }));
                }

                // 如果B成员检测到异常，发送特殊告警
                const recentAnomalies = detectionResults.recent_detections?.filter(r => r.is_anomaly) || [];
                if (recentAnomalies.length > 0) {
                    const latestAnomaly = recentAnomalies[recentAnomalies.length - 1];
                    client.send(JSON.stringify({
                        type: 'anomaly-alert',
                        data: {
                            timestamp: new Date(latestAnomaly.timestamp * 1000).toISOString(),
                            type: 'AI检测异常',
                            severity: latestAnomaly.severity || 'medium',
                            anomaly_score: latestAnomaly.anomaly_score,
                            threshold: latestAnomaly.threshold,
                            description: `检测到异常流量，异常分数: ${latestAnomaly.anomaly_score.toFixed(2)}`
                        }
                    }));
                }
            }
        });
    } catch (error) {
        console.error('推送实时数据失败:', error);

        // 如果Python模块出错，回退到原有的模拟数据
        const traffic = generateTrafficData();
        const alert = generateAlertData();

        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(JSON.stringify({ type: 'traffic', data: traffic }));
                if (alert) {
                    client.send(JSON.stringify({ type: 'alert', data: alert }));
                }
            }
        });
    }
}, 2000); // 每2秒更新一次（给Python模块更多处理时间）

// --- 启动服务器 ---
server.listen(PORT, () => {
    console.log(`Node.js backend running on http://localhost:${PORT}`);
    console.log(`Frontend served from http://localhost:${PORT}`);
});
