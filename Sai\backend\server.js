const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

const PORT = 3000;

// --- 静态文件服务 ---
// 将 frontend 文件夹设置为静态资源目录
app.use(express.static(path.join(__dirname, '../frontend')));

// --- API 数据模拟 ---

// 模拟网络拓扑数据
let topologyData = {
    nodes: [
        { id: 'h1', name: 'Host 1', type: 'host', ip: '********' },
        { id: 'h2', name: 'Host 2', type: 'host', ip: '********' },
        { id: 's1', name: 'Switch 1', type: 'switch', dpId: '00:00:00:00:00:00:00:01' },
        { id: 's2', name: 'Switch 2', type: 'switch', dpId: '00:00:00:00:00:00:00:02' }
    ],
    links: [
        { source: 'h1', target: 's1' },
        { source: 'h2', target: 's2' },
        { source: 's1', target: 's2' }
    ]
};

// 模拟流表数据
let flowTableData = [
    { id: 1, dpId: '00:00:00:00:00:00:00:01', match: 'in_port=1,eth_type=0x800', action: 'output:2', packetCount: 12345, byteCount: 98765432 },
    { id: 2, dpId: '00:00:00:00:00:00:00:01', match: 'in_port=2,eth_type=0x800', action: 'output:1', packetCount: 11223, byteCount: 88776655 },
    { id: 3, dpId: '00:00:00:00:00:00:00:02', match: 'in_port=1,eth_type=0x800', action: 'output:2', packetCount: 9876, byteCount: 76543210 }
];

// 模拟实时流量数据 (用于 WebSocket 推送)
let currentTraffic = 0;
let trafficHistory = []; // 存储历史数据点
const MAX_HISTORY_POINTS = 100; // 历史数据点数量

function generateTrafficData() {
    currentTraffic += (Math.random() - 0.5) * 20 + 50; // 模拟波动流量，平均50
    if (currentTraffic < 0) currentTraffic = 0;
    const now = new Date();
    trafficHistory.push({ time: now.toLocaleTimeString(), value: currentTraffic });
    if (trafficHistory.length > MAX_HISTORY_POINTS) {
        trafficHistory.shift(); // 移除最旧的数据点
    }
    return {
        timestamp: now.toISOString(),
        bandwidth: currentTraffic,
        packetRate: Math.floor(currentTraffic * 1.2 * (0.8 + Math.random() * 0.4)) // 模拟包速率
    };
}

// 模拟告警数据 (用于 WebSocket 推送)
const attackTypes = ['DDoS', 'Port Scan', 'SYN Flood', 'Brute Force'];
const sourceIPs = ['*************', '********', '***********', '*************', '********'];
const destinationIPs = ['********', '********', '********'];

function generateAlertData() {
    const isAttack = Math.random() < 0.3; // 30%的概率生成告警
    if (isAttack) {
        return {
            timestamp: new Date().toISOString(),
            type: attackTypes[Math.floor(Math.random() * attackTypes.length)],
            source: sourceIPs[Math.floor(Math.random() * sourceIPs.length)],
            destination: destinationIPs[Math.floor(Math.random() * destinationIPs.length)],
            severity: Math.random() < 0.6 ? 'High' : 'Medium',
            description: 'Suspicious activity detected.'
        };
    }
    return null; // 不总是生成告警
}

// --- RESTful API 路由 ---
app.get('/api/topology', (req, res) => {
    res.json(topologyData);
});

app.get('/api/flowtables', (req, res) => {
    res.json(flowTableData);
});

// --- WebSocket 服务 ---
wss.on('connection', ws => {
    console.log('Client connected via WebSocket');

    // 初始发送部分历史流量数据，让图表不为空
    ws.send(JSON.stringify({ type: 'trafficHistory', data: trafficHistory }));

    ws.on('close', () => {
        console.log('Client disconnected from WebSocket');
    });

    ws.on('error', error => {
        console.error('WebSocket error:', error);
    });
});

// 每秒推送实时数据到所有连接的客户端
setInterval(() => {
    const traffic = generateTrafficData();
    const alert = generateAlertData();

    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            // 推送流量数据
            client.send(JSON.stringify({ type: 'traffic', data: traffic }));
            // 推送告警数据 (如果有)
            if (alert) {
                client.send(JSON.stringify({ type: 'alert', data: alert }));
            }
        }
    });
}, 1000); // 每秒更新一次

// --- 启动服务器 ---
server.listen(PORT, () => {
    console.log(`Node.js backend running on http://localhost:${PORT}`);
    console.log(`Frontend served from http://localhost:${PORT}`);
});
