"""
测试脚本 - B成员模块测试
Test Script for Traffic Detection Modules

功能：
- 测试所有B成员开发的模块
- 验证模块功能正确性
- 生成测试报告和可视化结果
"""

import sys
import os
import numpy as np
import time

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from traffic_collector import TrafficCollector
from feature_extractor import FeatureExtractor
from dynamic_threshold import DynamicThreshold, MultiFeatureThreshold
from statistical_analyzer import StatisticalAnalyzer

def test_traffic_collector():
    """测试流量采集模块"""
    print("=" * 50)
    print("测试流量采集模块")
    print("=" * 50)
    
    collector = TrafficCollector()
    
    # 模拟生成流量数据
    collector.simulate_traffic_data(num_flows=30, duration=300)
    
    # 获取统计信息
    summary = collector.get_traffic_summary()
    print("流量摘要:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 获取流统计信息
    flow_stats = collector.get_flow_statistics()
    print(f"\n生成了 {len(flow_stats)} 个流的数据")
    
    # 导出数据
    collector.export_data("test_traffic_data.json")
    
    return flow_stats

def test_feature_extractor(flow_stats):
    """测试特征提取模块"""
    print("\n" + "=" * 50)
    print("测试特征提取模块")
    print("=" * 50)
    
    extractor = FeatureExtractor()
    
    # 测试单个流的特征提取
    if flow_stats:
        first_flow_key = list(flow_stats.keys())[0]
        first_flow = flow_stats[first_flow_key]
        
        print(f"测试流: {first_flow_key}")
        features = extractor.extract_all_features(first_flow)
        
        print("提取的特征:")
        for feature_name, value in features.items():
            print(f"  {feature_name}: {value:.4f}")
        
        # 获取特征向量
        feature_vector = extractor.get_feature_vector(first_flow)
        print(f"\n特征向量长度: {len(feature_vector)}")
        print(f"特征向量: {feature_vector[:5]}...")  # 只显示前5个值
    
    # 批量特征提取
    print(f"\n批量提取 {len(flow_stats)} 个流的特征...")
    feature_matrix = extractor.extract_batch_features(flow_stats)
    print(f"特征矩阵形状: {feature_matrix.shape}")
    print("特征矩阵前5行:")
    print(feature_matrix.head())
    
    return feature_matrix

def test_dynamic_threshold():
    """测试动态阈值检测模块"""
    print("\n" + "=" * 50)
    print("测试动态阈值检测模块")
    print("=" * 50)
    
    detector = DynamicThreshold(window_size=50, sensitivity=2.0)
    
    # 生成测试数据（正常数据 + 异常数据）
    print("生成测试数据...")
    normal_data = np.random.normal(100, 10, 80)
    anomaly_data = np.random.normal(150, 5, 20)
    test_data = np.concatenate([normal_data, anomaly_data])
    
    # 批量检测
    print("执行异常检测...")
    results = detector.batch_detect(test_data)
    
    # 输出统计信息
    stats = detector.get_detection_stats()
    print("检测统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 输出异常检测结果
    anomalies = [r for r in results if r['is_anomaly']]
    print(f"\n检测到 {len(anomalies)} 个异常")
    
    if anomalies:
        print("前5个异常:")
        for i, anomaly in enumerate(anomalies[:5]):
            print(f"  异常 {i+1}: 值={anomaly['value']:.2f}, "
                  f"阈值={anomaly['threshold']:.2f}, "
                  f"异常分数={anomaly['anomaly_score']:.2f}")
    
    # 导出结果
    detector.export_results("test_threshold_results.json")
    
    return results

def test_multi_feature_threshold(feature_matrix):
    """测试多特征动态阈值检测"""
    print("\n" + "=" * 50)
    print("测试多特征动态阈值检测")
    print("=" * 50)
    
    if feature_matrix.empty:
        print("没有特征数据，跳过多特征测试")
        return []
    
    feature_names = feature_matrix.columns.tolist()
    multi_detector = MultiFeatureThreshold(feature_names, window_size=30, sensitivity=2.0)
    
    print(f"使用 {len(feature_names)} 个特征进行检测")
    
    # 测试前10个样本
    test_samples = feature_matrix.head(10).values
    results = []
    
    for i, feature_vector in enumerate(test_samples):
        result = multi_detector.detect_multi_features(feature_vector, feature_names)
        results.append(result)
        
        if result['is_overall_anomaly']:
            print(f"样本 {i+1}: 检测到异常，异常特征: {result['anomaly_features']}")
    
    # 获取所有特征的统计信息
    all_stats = multi_detector.get_all_stats()
    print(f"\n各特征检测统计:")
    for feature_name, stats in all_stats.items():
        if stats['total_count'] > 0:
            print(f"  {feature_name}: 异常率 {stats['anomaly_rate']:.2%}")
    
    return results

def test_statistical_analyzer(flow_stats, detection_results):
    """测试统计分析模块"""
    print("\n" + "=" * 50)
    print("测试统计分析模块")
    print("=" * 50)
    
    analyzer = StatisticalAnalyzer()
    
    # 分析流量模式
    print("分析流量模式...")
    traffic_analysis = analyzer.analyze_traffic_patterns(flow_stats)
    
    print("流量模式分析结果:")
    print(f"  流数量: {traffic_analysis['flow_count']}")
    print(f"  平均包数量: {traffic_analysis['packet_stats']['mean']:.1f}")
    print(f"  平均字节数: {traffic_analysis['byte_stats']['mean']:.1f}")
    print(f"  总字节数: {traffic_analysis['byte_stats']['total']:.0f}")
    
    # 分析检测性能
    print("\n分析检测性能...")
    performance_analysis = analyzer.analyze_detection_performance(detection_results)
    
    print("检测性能分析结果:")
    print(f"  总检测次数: {performance_analysis['total_detections']}")
    print(f"  异常检测次数: {performance_analysis['anomaly_detections']}")
    print(f"  异常率: {performance_analysis['anomaly_rate']:.2%}")
    
    # 生成可视化图表
    print("\n生成可视化图表...")
    try:
        analyzer.generate_traffic_visualization(flow_stats, "test_traffic_visualization.png")
        analyzer.generate_threshold_visualization(detection_results, "test_threshold_visualization.png")
        print("可视化图表生成成功")
    except Exception as e:
        print(f"可视化图表生成失败: {e}")
    
    # 生成报告
    analyzer.generate_report("test_analysis_report.json")
    
    return analyzer

def main():
    """主测试函数"""
    print("开始测试B成员流量检测模块")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 1. 测试流量采集模块
        flow_stats = test_traffic_collector()
        
        # 2. 测试特征提取模块
        feature_matrix = test_feature_extractor(flow_stats)
        
        # 3. 测试动态阈值检测模块
        detection_results = test_dynamic_threshold()
        
        # 4. 测试多特征阈值检测
        multi_results = test_multi_feature_threshold(feature_matrix)
        
        # 5. 测试统计分析模块
        analyzer = test_statistical_analyzer(flow_stats, detection_results)
        
        print("\n" + "=" * 50)
        print("所有模块测试完成！")
        print("=" * 50)
        
        print("\n生成的文件:")
        print("  - test_traffic_data.json: 流量数据")
        print("  - test_threshold_results.json: 阈值检测结果")
        print("  - test_analysis_report.json: 分析报告")
        print("  - test_traffic_visualization.png: 流量可视化图表")
        print("  - test_threshold_visualization.png: 阈值检测可视化图表")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
