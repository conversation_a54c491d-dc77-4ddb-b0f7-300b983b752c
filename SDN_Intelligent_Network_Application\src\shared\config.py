#!/usr/bin/env python3
"""
系统配置文件 - 共享配置
统一管理所有模块的配置参数
"""

import os
from typing import Dict, Any

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
SRC_DIR = os.path.join(BASE_DIR, 'src')
DATA_DIR = os.path.join(BASE_DIR, 'data')
LOGS_DIR = os.path.join(DATA_DIR, 'logs')
MODELS_DIR = os.path.join(DATA_DIR, 'models')

# 确保目录存在
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(MODELS_DIR, exist_ok=True)

# 系统配置
SYSTEM_CONFIG: Dict[str, Any] = {
    # A成员 - SDN控制器配置
    'sdn_controller': {
        'controller_id': 'main_controller',
        'host': '127.0.0.1',
        'port': 6633,
        'openflow_version': '1.3',
        'flow_timeout': 300,
        'topology_discovery_interval': 10,
        'stats_update_interval': 1
    },
    
    # B成员 - 流量检测配置
    'traffic_detection': {
        'collector_id': 'main_collector',
        'sampling_rate': 1.0,
        'flow_timeout': 60,
        'max_packet_size': 1500,
        'feature_count': 23,
        'detection_threshold': 2.0,
        'window_size': 100,
        'cleanup_interval': 10
    },
    
    # C成员 - 机器学习配置
    'machine_learning': {
        'detector_id': 'ml_detector',
        'model_types': ['random_forest', 'svm', 'knn'],
        'default_model': 'random_forest',
        'training_ratio': 0.8,
        'cv_folds': 5,
        'max_iterations': 1000,
        'random_state': 42,
        'federated_learning': {
            'min_participants': 2,
            'max_rounds': 100,
            'convergence_threshold': 0.001,
            'aggregation_method': 'fedavg'
        }
    },
    
    # D成员 - 前端配置
    'frontend': {
        'backend_host': '127.0.0.1',
        'backend_port': 5000,
        'frontend_host': '127.0.0.1',
        'frontend_port': 3000,
        'websocket_port': 5001,
        'api_prefix': '/api/v1',
        'cors_origins': ['http://localhost:3000'],
        'update_interval': 2000  # ms
    },
    
    # 数据库配置
    'database': {
        'type': 'sqlite',
        'path': os.path.join(DATA_DIR, 'sdn_network.db'),
        'backup_interval': 3600  # 1小时
    },
    
    # 日志配置
    'logging': {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_rotation': 'daily',
        'max_files': 30,
        'max_size': '100MB'
    },
    
    # 性能配置
    'performance': {
        'max_memory_usage': 512,  # MB
        'max_cpu_usage': 80,      # %
        'monitoring_interval': 5,  # seconds
        'alert_thresholds': {
            'memory': 400,  # MB
            'cpu': 70,      # %
            'disk': 90      # %
        }
    },
    
    # 安全配置
    'security': {
        'api_key_required': False,
        'rate_limiting': {
            'enabled': True,
            'requests_per_minute': 100
        },
        'encryption': {
            'enabled': False,
            'algorithm': 'AES-256'
        }
    }
}

# 攻击类型配置
ATTACK_TYPES = {
    0: {
        'name': 'normal',
        'description': '正常流量',
        'severity': 'info',
        'color': '#28a745'
    },
    1: {
        'name': 'ddos',
        'description': 'DDoS攻击',
        'severity': 'high',
        'color': '#dc3545'
    },
    2: {
        'name': 'port_scan',
        'description': '端口扫描',
        'severity': 'medium',
        'color': '#ffc107'
    },
    3: {
        'name': 'data_exfiltration',
        'description': '数据泄露',
        'severity': 'critical',
        'color': '#6f42c1'
    }
}

# 特征名称配置
FEATURE_NAMES = [
    'packet_count', 'byte_count', 'duration', 'packet_rate', 'byte_rate',
    'avg_packet_size', 'std_packet_size', 'min_packet_size', 'max_packet_size',
    'entropy_packet_size', 'entropy_inter_arrival', 'kl_divergence',
    'hurst_exponent', 'fractal_dimension', 'lyapunov_exponent',
    'tcp_flag_ratio', 'syn_count', 'ack_count', 'fin_count', 'rst_count',
    'push_count', 'urg_count', 'protocol_ratio'
]

# 网络拓扑配置
NETWORK_TOPOLOGY = {
    'default_switches': [
        {
            'dpid': '00:00:00:00:00:00:00:01',
            'name': 'Core-Switch-1',
            'ip': '************',
            'ports': [1, 2, 3, 4, 5, 6]
        },
        {
            'dpid': '00:00:00:00:00:00:00:02',
            'name': 'Core-Switch-2',
            'ip': '************',
            'ports': [1, 2, 3, 4, 5, 6]
        },
        {
            'dpid': '00:00:00:00:00:00:00:03',
            'name': 'Edge-Switch-1',
            'ip': '************',
            'ports': [1, 2, 3, 4]
        }
    ],
    'default_hosts': [
        {
            'mac': '00:00:00:00:01:01',
            'ip': '*********',
            'name': 'Server-1'
        },
        {
            'mac': '00:00:00:00:01:02',
            'ip': '*********',
            'name': 'Server-2'
        },
        {
            'mac': '00:00:00:00:02:01',
            'ip': '*********',
            'name': 'Client-1'
        }
    ]
}

# 演示配置
DEMO_CONFIG = {
    'attack_scenarios': {
        'ddos': {
            'duration': 30,
            'packet_rate': 10000,
            'target_ip': '*********'
        },
        'port_scan': {
            'duration': 60,
            'scan_rate': 100,
            'port_range': (1, 1000)
        },
        'data_exfiltration': {
            'duration': 120,
            'data_rate': 1000000,
            'external_ip': '***********'
        }
    },
    'normal_traffic': {
        'flows_per_second': 50,
        'packet_size_range': (64, 1500),
        'protocols': ['TCP', 'UDP', 'ICMP'],
        'protocol_distribution': [0.7, 0.2, 0.1]
    }
}

def get_config(section: str = None) -> Dict[str, Any]:
    """获取配置"""
    if section is None:
        return SYSTEM_CONFIG
    return SYSTEM_CONFIG.get(section, {})

def update_config(section: str, key: str, value: Any) -> bool:
    """更新配置"""
    try:
        if section in SYSTEM_CONFIG:
            SYSTEM_CONFIG[section][key] = value
            return True
        return False
    except Exception:
        return False

def get_log_file_path(module_name: str) -> str:
    """获取日志文件路径"""
    return os.path.join(LOGS_DIR, f"{module_name}.log")

def get_model_file_path(model_name: str) -> str:
    """获取模型文件路径"""
    return os.path.join(MODELS_DIR, f"{model_name}.joblib")

def get_data_file_path(filename: str) -> str:
    """获取数据文件路径"""
    return os.path.join(DATA_DIR, filename)

# 环境变量配置
def load_env_config():
    """从环境变量加载配置"""
    # SDN控制器
    if os.getenv('SDN_CONTROLLER_HOST'):
        SYSTEM_CONFIG['sdn_controller']['host'] = os.getenv('SDN_CONTROLLER_HOST')
    if os.getenv('SDN_CONTROLLER_PORT'):
        SYSTEM_CONFIG['sdn_controller']['port'] = int(os.getenv('SDN_CONTROLLER_PORT'))
    
    # 前端
    if os.getenv('FRONTEND_HOST'):
        SYSTEM_CONFIG['frontend']['frontend_host'] = os.getenv('FRONTEND_HOST')
    if os.getenv('FRONTEND_PORT'):
        SYSTEM_CONFIG['frontend']['frontend_port'] = int(os.getenv('FRONTEND_PORT'))
    
    # 日志级别
    if os.getenv('LOG_LEVEL'):
        SYSTEM_CONFIG['logging']['level'] = os.getenv('LOG_LEVEL')

# 加载环境变量配置
load_env_config()

if __name__ == "__main__":
    import json
    print("系统配置:")
    print(json.dumps(SYSTEM_CONFIG, indent=2, ensure_ascii=False))
