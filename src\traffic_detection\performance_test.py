"""
性能测试模块
Performance Test Module

功能：
- 测试各模块的处理速度
- 内存使用监控
- 准确率评估
- 压力测试
"""

import time
import psutil
import numpy as np
import pandas as pd
from memory_profiler import profile
import gc

from traffic_collector import TrafficCollector
from feature_extractor import FeatureExtractor
from dynamic_threshold import DynamicThreshold, AdvancedAnomalyDetector
from statistical_analyzer import StatisticalAnalyzer

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        """初始化性能测试器"""
        self.results = {}
        
    def measure_time(self, func, *args, **kwargs):
        """测量函数执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
        
    def measure_memory(self, func, *args, **kwargs):
        """测量函数内存使用"""
        process = psutil.Process()
        
        # 垃圾回收
        gc.collect()
        
        # 测量前内存
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 测量后内存
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = memory_after - memory_before
        
        return result, memory_used
        
    def test_traffic_collector_performance(self):
        """测试流量采集器性能"""
        print("=== 流量采集器性能测试 ===")
        
        collector = TrafficCollector()
        
        # 测试不同规模的数据生成
        test_sizes = [100, 500, 1000, 2000]
        results = {}
        
        for size in test_sizes:
            print(f"测试 {size} 个流的生成...")
            
            # 测量时间
            _, exec_time = self.measure_time(
                collector.simulate_traffic_data, 
                num_flows=size, 
                duration=300
            )
            
            # 测量内存
            collector_clean = TrafficCollector()
            _, memory_used = self.measure_memory(
                collector_clean.simulate_traffic_data,
                num_flows=size,
                duration=300
            )
            
            # 计算处理速度
            flows_per_second = size / exec_time if exec_time > 0 else 0
            
            results[size] = {
                'execution_time': exec_time,
                'memory_used': memory_used,
                'flows_per_second': flows_per_second
            }
            
            print(f"  执行时间: {exec_time:.3f}s")
            print(f"  内存使用: {memory_used:.2f}MB")
            print(f"  处理速度: {flows_per_second:.1f} flows/s")
            
        self.results['traffic_collector'] = results
        return results
        
    def test_feature_extractor_performance(self):
        """测试特征提取器性能"""
        print("\n=== 特征提取器性能测试 ===")
        
        # 准备测试数据
        collector = TrafficCollector()
        collector.simulate_traffic_data(num_flows=1000, duration=300)
        flow_stats = collector.get_flow_statistics()
        
        extractor = FeatureExtractor()
        
        # 测试单个流特征提取
        first_flow = list(flow_stats.values())[0]
        _, single_time = self.measure_time(
            extractor.extract_all_features,
            first_flow
        )
        
        # 测试批量特征提取
        test_sizes = [100, 500, 1000]
        results = {}
        
        for size in test_sizes:
            print(f"测试 {size} 个流的特征提取...")
            
            # 选择子集
            subset_flows = dict(list(flow_stats.items())[:size])
            
            # 测量时间和内存
            _, exec_time = self.measure_time(
                extractor.extract_batch_features,
                subset_flows
            )
            
            _, memory_used = self.measure_memory(
                extractor.extract_batch_features,
                subset_flows
            )
            
            # 计算处理速度
            flows_per_second = size / exec_time if exec_time > 0 else 0
            
            results[size] = {
                'execution_time': exec_time,
                'memory_used': memory_used,
                'flows_per_second': flows_per_second,
                'single_flow_time': single_time
            }
            
            print(f"  执行时间: {exec_time:.3f}s")
            print(f"  内存使用: {memory_used:.2f}MB")
            print(f"  处理速度: {flows_per_second:.1f} flows/s")
            
        self.results['feature_extractor'] = results
        return results
        
    def test_threshold_detector_performance(self):
        """测试阈值检测器性能"""
        print("\n=== 阈值检测器性能测试 ===")
        
        detector = DynamicThreshold(window_size=100, sensitivity=2.0)
        
        # 测试不同数据量的检测
        test_sizes = [1000, 5000, 10000, 20000]
        results = {}
        
        for size in test_sizes:
            print(f"测试 {size} 个数据点的检测...")
            
            # 生成测试数据
            test_data = np.random.normal(100, 10, size)
            
            # 测量时间和内存
            _, exec_time = self.measure_time(
                detector.batch_detect,
                test_data
            )
            
            detector_clean = DynamicThreshold(window_size=100, sensitivity=2.0)
            _, memory_used = self.measure_memory(
                detector_clean.batch_detect,
                test_data
            )
            
            # 计算处理速度
            points_per_second = size / exec_time if exec_time > 0 else 0
            
            results[size] = {
                'execution_time': exec_time,
                'memory_used': memory_used,
                'points_per_second': points_per_second
            }
            
            print(f"  执行时间: {exec_time:.3f}s")
            print(f"  内存使用: {memory_used:.2f}MB")
            print(f"  处理速度: {points_per_second:.1f} points/s")
            
        self.results['threshold_detector'] = results
        return results
        
    def test_accuracy_performance(self):
        """测试检测准确率"""
        print("\n=== 检测准确率测试 ===")
        
        # 生成带标签的测试数据
        normal_data = np.random.normal(100, 10, 800)
        anomaly_data = np.random.normal(150, 5, 200)
        
        test_data = np.concatenate([normal_data, anomaly_data])
        true_labels = np.concatenate([np.zeros(800), np.ones(200)])
        
        # 测试动态阈值检测
        detector = DynamicThreshold(window_size=50, sensitivity=2.0)
        results = detector.batch_detect(test_data)
        
        predicted_labels = [1 if r['is_anomaly'] else 0 for r in results]
        
        # 计算准确率指标
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        accuracy = accuracy_score(true_labels, predicted_labels)
        precision = precision_score(true_labels, predicted_labels)
        recall = recall_score(true_labels, predicted_labels)
        f1 = f1_score(true_labels, predicted_labels)
        
        # 计算误报率和漏报率
        tp = sum((t == 1) and (p == 1) for t, p in zip(true_labels, predicted_labels))
        fp = sum((t == 0) and (p == 1) for t, p in zip(true_labels, predicted_labels))
        tn = sum((t == 0) and (p == 0) for t, p in zip(true_labels, predicted_labels))
        fn = sum((t == 1) and (p == 0) for t, p in zip(true_labels, predicted_labels))
        
        false_positive_rate = fp / (fp + tn) if (fp + tn) > 0 else 0
        false_negative_rate = fn / (fn + tp) if (fn + tp) > 0 else 0
        
        accuracy_results = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'false_positive_rate': false_positive_rate,
            'false_negative_rate': false_negative_rate,
            'confusion_matrix': {'tp': tp, 'fp': fp, 'tn': tn, 'fn': fn}
        }
        
        print(f"  准确率: {accuracy:.3f}")
        print(f"  精确率: {precision:.3f}")
        print(f"  召回率: {recall:.3f}")
        print(f"  F1分数: {f1:.3f}")
        print(f"  误报率: {false_positive_rate:.3f}")
        print(f"  漏报率: {false_negative_rate:.3f}")
        
        self.results['accuracy'] = accuracy_results
        return accuracy_results
        
    def stress_test(self):
        """压力测试"""
        print("\n=== 压力测试 ===")
        
        # 大数据量测试
        print("大数据量测试...")
        collector = TrafficCollector()
        
        start_time = time.time()
        collector.simulate_traffic_data(num_flows=10000, duration=3600)
        end_time = time.time()
        
        print(f"  生成10000个流耗时: {end_time - start_time:.2f}s")
        
        # 长时间运行测试
        print("长时间运行测试...")
        detector = DynamicThreshold(window_size=1000, sensitivity=2.0)
        
        start_time = time.time()
        for i in range(100):  # 模拟100轮检测
            test_data = np.random.normal(100, 10, 1000)
            detector.batch_detect(test_data)
            
        end_time = time.time()
        print(f"  100轮检测耗时: {end_time - start_time:.2f}s")
        
        # 内存泄漏测试
        print("内存泄漏测试...")
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        for i in range(50):
            collector_temp = TrafficCollector()
            collector_temp.simulate_traffic_data(num_flows=100, duration=60)
            del collector_temp
            gc.collect()
            
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - initial_memory
        
        print(f"  内存增长: {memory_increase:.2f}MB")
        
        stress_results = {
            'large_data_time': end_time - start_time,
            'memory_increase': memory_increase
        }
        
        self.results['stress_test'] = stress_results
        return stress_results
        
    def generate_performance_report(self):
        """生成性能测试报告"""
        print("\n" + "=" * 60)
        print("性能测试报告")
        print("=" * 60)
        
        # 汇总关键指标
        summary = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'test_results': self.results
        }
        
        # 保存报告
        import json
        with open('performance_report.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
            
        print("性能测试报告已保存到: performance_report.json")
        
        return summary
        
    def run_all_tests(self):
        """运行所有性能测试"""
        print("开始性能测试...")
        
        try:
            self.test_traffic_collector_performance()
            self.test_feature_extractor_performance()
            self.test_threshold_detector_performance()
            self.test_accuracy_performance()
            self.stress_test()
            
            self.generate_performance_report()
            
            print("\n所有性能测试完成！")
            
        except Exception as e:
            print(f"性能测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    tester = PerformanceTester()
    tester.run_all_tests()
