# SDN 威胁检测可视化平台 (前端与Node.js后端)


它包含一个轻量级的 Node.js 后端和一个无框架（Vanilla JS）前端
**技术栈:**
- **后端:** Node.js, Express.js, ws (WebSocket)
- **前端:** HTML5, CSS3, Vanilla JavaScript
- **可视化库:** ECharts.js


## 目录结构

```
.
├── backend/
│   ├── node_modules/
│   ├── package.json
│   └── server.js      <-- 核心后端逻辑与数据模拟
├── frontend/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js    <-- 核心前端逻辑
│   └── index.html
└── README.md
```

---

在运行前，请确保电脑已安装 [Node.js]
1.  **进入后端目录**
    打开终端，进入 `backend` 文件夹。
    ```bash
    cd backend
    ```

2.  **安装依赖**
    执行以下命令来安装 `express` 和 `ws`。
    ```bash
    npm install
    ```

3.  **启动服务**这一步可以用 输入node server.js运行项目来代替

    此命令会启动 Node.js 服务器。
    ```bash
    npm start
    ```
    启动成功后，会看到类似以下的输出：
    ```
    Node.js backend running on http://localhost:3000
    Frontend served from http://localhost:3000
    ```

4.  **查看界面**
    在你的浏览器中打开 `http://localhost:3000` 即可看到可视化界面。
 目前所有显示的数据均为 模拟数据，用于前端独立开发和效果展示。下一步的核心工作就是替换为真实数据。

---

## 协同开发指南：如何接入真实数据

为了将项目各模块整合，其他队员需要将真实数据接入到本模块的后端。**所有修改都集中在 `backend/server.js` 文件中。**

### 📍 **给队员 A (架构设计 / SDN控制器)**

你需要替换网络拓扑和流表的模拟数据。

1.  **修改网络拓扑数据 (`GET /api/topology`)**
    -   在 `server.js` 中找到 `topologyData` 变量。
    -   你需要修改 `/api/topology` 路由处理函数，删除静态的 `topologyData`，改为通过 **SDN 控制器的北向接口 (REST API)** 实时获取拓扑数据，并将其返回。
    -   **示例 (思路):**
        ```javascript
        // const axios = require('axios'); // 你可能需要安装 axios
        
        app.get('/api/topology', async (req, res) => {
            try {
                // const response = await axios.get('http://<控制器IP>:<端口>/sdn/v1/topology');
                // res.json(response.data); // 返回从控制器获取的真实数据
                
                // --- 当前模拟数据，请替换 ---
                let topologyData = { ... }; 
                res.json(topologyData);
                // --------------------------

            } catch (error) {
                res.status(500).json({ error: 'Failed to fetch topology from controller' });
            }
        });
        ```

2.  **修改流表数据 (`GET /api/flowtables`)**
    -   与拓扑数据类似，在 `server.js` 中找到 `/api/flowtables` 路由。
    -   修改其实现，调用 **SDN 控制器的 API** 获取所有交换机的流表信息，并返回给前端。

### 📍 **给队员 B (流量检测) 和 队员 C (智能检测)**

你们需要替换实时流量和告警的模拟数据。这些数据是通过 WebSocket 实时推送给前端的。

1.  **修改实时流量数据**
    -   在 `server.js` 中找到 `generateTrafficData()` 函数。
    -   这个函数目前每秒生成随机的流量数据。你需要修改它，使其从你的 **流量特征提取模块** 获取真实的流量统计数据（如带宽、包速率等）。
    -   函数必须返回一个与当前结构一致的对象：`{ timestamp, bandwidth, packetRate }`。

2.  **修改实时告警数据 (最关键)**
    -   在 `server.js` 中找到 `generateAlertData()` 函数。
    -   这个函数目前以一定概率随机生成告警。你需要修改它，使其能够 **接收** 来自你们检测模块的真实告警。
    -   **集成方式建议：**
        *   **方法一 (轮询 API):** 你们的检测模块提供一个 API 接口，`server.js` 中的 `generateAlertData` 函数定期（如每秒）调用该接口来检查是否有新告警。
        *   **方法二 (消息推送):** 你们的检测模块在发现攻击时，主动调用 `server.js` 提供的一个新接口（例如，你可以新增一个 `POST /api/alert` 接口），将告警数据推送过来。
    -   **重要：** 无论使用哪种方法，最终传递给前端的告警数据格式必须为以下 JSON 对象，否则前端无法正确渲染：
        ```json
        {
          "timestamp": "ISO格式时间字符串",
          "type": "攻击类型 (如 DDoS)",
          "source": "源IP",
          "destination": "目的IP",
          "severity": "严重性 (High 或 Medium)",
          "description": "告警描述"
        }
        ```
    -   当没有告警时，`generateAlertData` 函数应返回 `null`。

---

如有任何关于前端或此后端服务的问题，请随时与 **队员 D** 联系。