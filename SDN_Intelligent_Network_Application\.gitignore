# SDN智能网络应用 - Git忽略文件

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
# 日志文件
*.log
data/logs/*.log

# 模型文件
data/models/*.joblib
data/models/*.pkl
data/models/*.h5

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 测试报告
test_*.json
*_report.json
*_results.json

# 数据文件
*.csv
*.json
data/*.csv
data/*.json

# 配置文件（如果包含敏感信息）
config.local.py
.env.local

# 性能分析文件
*.prof
*.profile
