#!/usr/bin/env python3
"""
B成员快速开始脚本
Quick Start Script for Member B

功能：
1. 验证开发环境
2. 测试基础模块
3. 生成示例输出
4. 检查代码质量
"""

import sys
import os
import time
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.7+")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'numpy', 'pandas', 'scipy', 'matplotlib', 
        'seaborn', 'scikit-learn', 'scapy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_network_permissions():
    """检查网络权限"""
    print("\n🌐 检查网络权限...")
    
    try:
        from scapy.all import get_if_list
        interfaces = get_if_list()
        if interfaces:
            print(f"✅ 网络接口检测成功，找到 {len(interfaces)} 个接口")
            print(f"   可用接口: {interfaces[:3]}...")  # 只显示前3个
            return True
        else:
            print("⚠️  未找到网络接口")
            return False
    except Exception as e:
        print(f"⚠️  网络权限检查失败: {e}")
        print("   提示: 可能需要管理员权限或使用模拟数据")
        return False

def test_basic_modules():
    """测试基础模块"""
    print("\n🧪 测试基础模块...")
    
    # 添加模块路径
    traffic_detection_path = Path(__file__).parent / "traffic_detection"
    sys.path.insert(0, str(traffic_detection_path))
    
    try:
        # 测试流量采集模块
        print("  测试流量采集模块...")
        from traffic_collector import TrafficCollector
        collector = TrafficCollector()
        collector.simulate_traffic_data(num_flows=10, duration=60)
        print("  ✅ 流量采集模块正常")
        
        # 测试特征提取模块
        print("  测试特征提取模块...")
        from feature_extractor import FeatureExtractor
        extractor = FeatureExtractor()
        flow_stats = collector.get_flow_statistics()
        if flow_stats:
            first_flow = list(flow_stats.values())[0]
            features = extractor.extract_all_features(first_flow)
            print(f"  ✅ 特征提取模块正常，提取了 {len(features)} 个特征")
        
        # 测试动态阈值模块
        print("  测试动态阈值模块...")
        from dynamic_threshold import DynamicThreshold
        detector = DynamicThreshold(window_size=20, sensitivity=2.0)
        test_data = [100, 105, 98, 102, 150, 95, 101]  # 包含一个异常值
        results = detector.batch_detect(test_data)
        anomalies = [r for r in results if r['is_anomaly']]
        print(f"  ✅ 动态阈值模块正常，检测到 {len(anomalies)} 个异常")
        
        # 测试统计分析模块
        print("  测试统计分析模块...")
        from statistical_analyzer import StatisticalAnalyzer
        analyzer = StatisticalAnalyzer()
        traffic_analysis = analyzer.analyze_traffic_patterns(flow_stats)
        print(f"  ✅ 统计分析模块正常，分析了 {traffic_analysis.get('flow_count', 0)} 个流")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_sample_output():
    """生成示例输出"""
    print("\n📊 生成示例输出...")
    
    try:
        # 切换到traffic_detection目录
        original_dir = os.getcwd()
        traffic_detection_dir = Path(__file__).parent / "traffic_detection"
        os.chdir(traffic_detection_dir)
        
        # 运行测试模块
        print("  运行完整测试...")
        result = subprocess.run([sys.executable, "test_modules.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("  ✅ 测试完成，生成了以下文件:")
            
            # 检查生成的文件
            output_files = [
                "test_traffic_data.json",
                "test_threshold_results.json", 
                "test_analysis_report.json",
                "test_traffic_visualization.png",
                "test_threshold_visualization.png"
            ]
            
            for file in output_files:
                if Path(file).exists():
                    size = Path(file).stat().st_size
                    print(f"    📄 {file} ({size} bytes)")
                else:
                    print(f"    ⚠️  {file} - 未生成")
            
            return True
        else:
            print(f"  ❌ 测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⚠️  测试超时，可能需要更长时间")
        return False
    except Exception as e:
        print(f"  ❌ 生成示例输出失败: {e}")
        return False
    finally:
        os.chdir(original_dir)

def check_code_quality():
    """检查代码质量"""
    print("\n🔍 检查代码质量...")
    
    traffic_detection_dir = Path(__file__).parent / "traffic_detection"
    python_files = list(traffic_detection_dir.glob("*.py"))
    
    total_lines = 0
    total_comments = 0
    total_functions = 0
    
    for file_path in python_files:
        if file_path.name.startswith('__'):
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        file_lines = len(lines)
        file_comments = sum(1 for line in lines if line.strip().startswith('#') or '"""' in line)
        file_functions = sum(1 for line in lines if line.strip().startswith('def '))
        
        total_lines += file_lines
        total_comments += file_comments
        total_functions += file_functions
        
        print(f"  📄 {file_path.name}: {file_lines} 行, {file_functions} 函数, {file_comments} 注释")
    
    comment_ratio = (total_comments / total_lines * 100) if total_lines > 0 else 0
    
    print(f"\n  📊 代码统计:")
    print(f"    总行数: {total_lines}")
    print(f"    总函数: {total_functions}")
    print(f"    注释率: {comment_ratio:.1f}%")
    
    if comment_ratio >= 15:
        print("  ✅ 注释率良好")
    else:
        print("  ⚠️  建议增加注释")
    
    return True

def show_next_steps():
    """显示下一步操作"""
    print("\n🚀 下一步操作建议:")
    print("1. 如果环境检查通过，开始按照开发计划实施")
    print("2. 查看详细计划: src/B成员详细实现计划.md")
    print("3. 查看任务清单: src/B成员开发任务清单.md")
    print("4. 开始第一阶段开发:")
    print("   - 完善流量采集模块")
    print("   - 增加攻击流量模拟")
    print("   - 优化特征提取算法")
    print("\n📚 有用的命令:")
    print("   cd src/traffic_detection")
    print("   python test_modules.py          # 运行完整测试")
    print("   python traffic_collector.py     # 测试流量采集")
    print("   python feature_extractor.py     # 测试特征提取")
    print("   python dynamic_threshold.py     # 测试动态阈值")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 B成员开发环境检查与快速开始")
    print("=" * 60)
    
    start_time = time.time()
    
    # 检查列表
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("网络权限", check_network_permissions),
        ("基础模块", test_basic_modules),
        ("示例输出", generate_sample_output),
        ("代码质量", check_code_quality)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results[check_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 检查结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项检查通过")
    
    if passed >= total - 1:  # 允许一项失败（通常是网络权限）
        print("🎉 环境准备就绪，可以开始开发！")
        show_next_steps()
    else:
        print("⚠️  请解决上述问题后再开始开发")
    
    elapsed_time = time.time() - start_time
    print(f"\n⏱️  检查耗时: {elapsed_time:.1f} 秒")

if __name__ == "__main__":
    main()
