{"_from": "on-finished@2.4.1", "_id": "on-finished@2.4.1", "_inBundle": false, "_integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "_location": "/on-finished", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "on-finished@2.4.1", "name": "on-finished", "escapedName": "on-finished", "rawSpec": "2.4.1", "saveSpec": null, "fetchSpec": "2.4.1"}, "_requiredBy": ["/body-parser", "/express", "/finalhandler", "/send"], "_resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "_shasum": "58c8c44116e54845ad57f14ab10b03533184ac3f", "_spec": "on-finished@2.4.1", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"ee-first": "1.1.1"}, "deprecated": false, "description": "Execute a callback when a request closes, finishes, or errors", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/on-finished#readme", "license": "MIT", "name": "on-finished", "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-finished.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.4.1"}