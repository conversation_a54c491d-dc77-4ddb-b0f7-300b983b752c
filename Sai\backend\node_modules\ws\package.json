{"_from": "ws@^8.18.0", "_id": "ws@8.18.2", "_inBundle": false, "_integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "_location": "/ws", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ws@^8.18.0", "name": "ws", "escapedName": "ws", "rawSpec": "^8.18.0", "saveSpec": null, "fetchSpec": "^8.18.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "_shasum": "42738b2be57ced85f46154320aabb51ab003705a", "_spec": "ws@^8.18.0", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "browser": "browser.js", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "devDependencies": {"benchmark": "^2.1.4", "bufferutil": "^4.0.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.0.0", "globals": "^16.0.0", "mocha": "^8.4.0", "nyc": "^15.0.0", "prettier": "^3.0.0", "utf-8-validate": "^6.0.0"}, "engines": {"node": ">=10.0.0"}, "exports": {".": {"browser": "./browser.js", "import": "./wrapper.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "files": ["browser.js", "index.js", "lib/*.js", "wrapper.mjs"], "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "license": "MIT", "main": "index.js", "name": "ws", "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "scripts": {"integration": "mocha --throw-deprecation test/*.integration.js", "lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js"}, "version": "8.18.2"}