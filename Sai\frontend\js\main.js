document.addEventListener('DOMContentLoaded', () => {
    const backendUrl = 'http://localhost:3000';
    const wsUrl = 'ws://localhost:3000';

    // --- DOM 元素引用 ---
    const topologyContainer = document.getElementById('topology-container');
    const flowTableBody = document.getElementById('flow-table-body');
    const trafficChartDom = document.getElementById('traffic-chart');
    const alertList = document.getElementById('alert-list');

    // --- ECharts 流量图初始化 ---
    const trafficChart = echarts.init(trafficChartDom);
    let trafficXData = []; // 时间轴
    let trafficYData = []; // 流量值

    const trafficChartOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                params = params[0];
                return `时间: ${params.name}<br>流量: ${params.value} Mbps`;
            },
            axisPointer: {
                animation: false
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: trafficXData
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 200, // 根据实际流量范围调整
            splitLine: {
                show: true
            },
            name: '流量 (Mbps)'
        },
        series: [{
            name: '实时流量',
            type: 'line',
            showSymbol: false,
            data: trafficYData,
            lineStyle: {
                color: '#5470C6',
                width: 2
            },
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(84,112,198,0.5)'
                }, {
                    offset: 1,
                    color: 'rgba(84,112,198,0)'
                }])
            }
        }]
    };
    trafficChart.setOption(trafficChartOption);

    // --- 数据获取与渲染函数 ---

    // 获取并渲染网络拓扑
    async function fetchTopology() {
        try {
            const response = await fetch(`${backendUrl}/api/topology`);
            const data = await response.json();
            console.log('拓扑数据:', data);
            
            // 简单地以文本形式展示拓扑，你可以用D3.js等库绘制图形
            let topologyHtml = '<h3>节点:</h3><ul>';
            data.nodes.forEach(node => {
                topologyHtml += `<li>${node.name} (${node.type}, ${node.ip || node.dpId})</li>`;
            });
            topologyHtml += '</ul><h3>链路:</h3><ul>';
            data.links.forEach(link => {
                topologyHtml += `<li>${data.nodes.find(n => n.id === link.source)?.name} --- ${data.nodes.find(n => n.id === link.target)?.name}</li>`;
            });
            topologyHtml += '</ul>';
            topologyContainer.innerHTML = topologyHtml;

        } catch (error) {
            console.error('获取拓扑数据失败:', error);
            topologyContainer.innerHTML = '<p style="color: red;">加载拓扑失败。</p>';
        }
    }

    // 获取并渲染流表信息
    async function fetchFlowTables() {
        try {
            const response = await fetch(`${backendUrl}/api/flowtables`);
            const data = await response.json();
            console.log('流表数据:', data);

            flowTableBody.innerHTML = ''; // 清空现有内容
            if (data.length === 0) {
                flowTableBody.innerHTML = '<tr><td colspan="6">暂无流表信息。</td></tr>';
                return;
            }

            data.forEach(flow => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${flow.id}</td>
                    <td>${flow.dpId.substring(12)}</td> <!-- 简化DPID显示 -->
                    <td>${flow.match}</td>
                    <td>${flow.action}</td>
                    <td>${flow.packetCount}</td>
                    <td>${flow.byteCount}</td>
                `;
                flowTableBody.appendChild(row);
            });
        } catch (error) {
            console.error('获取流表数据失败:', error);
            flowTableBody.innerHTML = '<tr><td colspan="6" style="color: red;">加载流表失败。</td></tr>';
        }
    }

    // --- B成员数据存储 ---
    let realtimeStats = {};
    let detectionResults = [];
    let performanceMetrics = {};

    // --- B成员数据获取函数 ---
    async function fetchBMemberData() {
        try {
            // 获取实时统计
            const statsResponse = await fetch(`${backendUrl}/api/realtime-stats`);
            realtimeStats = await statsResponse.json();
            updateBMemberStats(realtimeStats);

            // 获取检测结果
            const detectionResponse = await fetch(`${backendUrl}/api/detection-results`);
            const detectionData = await detectionResponse.json();
            detectionResults = detectionData.recent_detections || [];
            updateDetectionResults(detectionData);

            // 获取性能指标
            const metricsResponse = await fetch(`${backendUrl}/api/performance-metrics`);
            performanceMetrics = await metricsResponse.json();
            updatePerformanceMetrics(performanceMetrics);

        } catch (error) {
            console.error('获取B成员数据失败:', error);
        }
    }

    // --- WebSocket 连接 ---
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
        console.log('WebSocket 连接成功');
        // 连接成功后立即获取B成员数据
        fetchBMemberData();
    };

    ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        // console.log('收到 WebSocket 消息:', message);

        if (message.type === 'traffic') {
            const trafficData = message.data;
            // 更新 ECharts 图表数据
            trafficXData.push(new Date(trafficData.timestamp).toLocaleTimeString());
            trafficYData.push(trafficData.bandwidth);

            // 保持数据点数量，移除最旧的
            if (trafficXData.length > 100) { // 与后端 MAX_HISTORY_POINTS 保持一致
                trafficXData.shift();
                trafficYData.shift();
            }

            trafficChart.setOption({
                xAxis: {
                    data: trafficXData
                },
                series: [{
                    data: trafficYData
                }]
            });
        } else if (message.type === 'trafficHistory') {
            // 接收历史数据，填充图表
            message.data.forEach(item => {
                trafficXData.push(item.time);
                trafficYData.push(item.value);
            });
            trafficChart.setOption({
                xAxis: { data: trafficXData },
                series: [{ data: trafficYData }]
            });
        }
        else if (message.type === 'alert') {
            const alertData = message.data;
            addAlert(alertData);
        }
        else if (message.type === 'realtime-stats') {
            // B成员实时统计数据
            realtimeStats = message.data;
            updateBMemberStats(message.data);
        }
        else if (message.type === 'detection-results') {
            // B成员检测结果
            detectionResults = message.data.recent_detections || [];
            updateDetectionResults(message.data);
        }
        else if (message.type === 'anomaly-alert') {
            // B成员异常告警
            addAnomalyAlert(message.data);
        }
    };

    ws.onclose = () => {
        console.warn('WebSocket 连接已关闭');
    };

    ws.onerror = (error) => {
        console.error('WebSocket 错误:', error);
    };

    // --- B成员数据处理函数 ---
    function updateBMemberStats(stats) {
        // 更新实时统计显示
        const statsContainer = document.getElementById('bmember-stats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">流量速度:</span>
                    <span class="stat-value">${stats.flows_per_second?.toFixed(1) || 0} flows/s</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">包速度:</span>
                    <span class="stat-value">${stats.packets_per_second?.toFixed(1) || 0} packets/s</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">异常率:</span>
                    <span class="stat-value ${stats.anomaly_rate > 0.1 ? 'alert' : ''}">${(stats.anomaly_rate * 100)?.toFixed(1) || 0}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">当前阈值:</span>
                    <span class="stat-value">${stats.current_threshold?.toFixed(2) || 0}</span>
                </div>
            `;
        }
    }

    function updateDetectionResults(data) {
        const resultsContainer = document.getElementById('detection-results');
        if (resultsContainer && data.recent_detections) {
            let html = '<h4>最近检测结果:</h4><ul>';
            data.recent_detections.slice(0, 5).forEach(result => {
                const timestamp = new Date(result.timestamp * 1000).toLocaleTimeString();
                const statusClass = result.is_anomaly ? 'anomaly' : 'normal';
                html += `
                    <li class="${statusClass}">
                        <span class="time">[${timestamp}]</span>
                        <span class="value">值: ${result.value?.toFixed(2)}</span>
                        <span class="score">分数: ${result.anomaly_score?.toFixed(2)}</span>
                        ${result.is_anomaly ? '<span class="alert-badge">异常</span>' : ''}
                    </li>
                `;
            });
            html += '</ul>';
            resultsContainer.innerHTML = html;
        }
    }

    function updatePerformanceMetrics(metrics) {
        const metricsContainer = document.getElementById('performance-metrics');
        if (metricsContainer) {
            metricsContainer.innerHTML = `
                <div class="metric-item">
                    <span class="metric-label">处理速度:</span>
                    <span class="metric-value">${metrics.processing_speed || 0} flows/s</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">检测准确率:</span>
                    <span class="metric-value">${(metrics.detection_accuracy * 100)?.toFixed(1) || 0}%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">内存使用:</span>
                    <span class="metric-value">${metrics.memory_usage?.toFixed(1) || 0} MB</span>
                </div>
            `;
        }
    }

    function addAlert(alertData) {
        const listItem = document.createElement('li');
        listItem.classList.add(`severity-${alertData.severity}`);
        listItem.innerHTML = `
            <strong>[${new Date(alertData.timestamp).toLocaleTimeString()}]</strong>
            <strong>${alertData.type}</strong> 告警: ${alertData.source} -> ${alertData.destination} - ${alertData.description}
            (严重性: ${alertData.severity})
        `;
        alertList.prepend(listItem);

        // 限制告警数量
        if (alertList.children.length > 50) {
            alertList.lastChild.remove();
        }
    }

    function addAnomalyAlert(alertData) {
        const listItem = document.createElement('li');
        listItem.classList.add(`severity-${alertData.severity}`, 'ai-detection');
        listItem.innerHTML = `
            <strong>[${new Date(alertData.timestamp).toLocaleTimeString()}]</strong>
            <strong>🤖 ${alertData.type}</strong>: ${alertData.description}
            <br><small>异常分数: ${alertData.anomaly_score?.toFixed(2)}, 阈值: ${alertData.threshold?.toFixed(2)}</small>
        `;
        alertList.prepend(listItem);

        // 限制告警数量
        if (alertList.children.length > 50) {
            alertList.lastChild.remove();
        }
    }

    // --- B成员控制按钮 ---
    function setupBMemberControls() {
        // 触发检测按钮
        const triggerBtn = document.getElementById('trigger-detection-btn');
        if (triggerBtn) {
            triggerBtn.addEventListener('click', async () => {
                try {
                    const response = await fetch(`${backendUrl}/api/trigger-detection`, {
                        method: 'POST'
                    });
                    const result = await response.json();
                    if (result.success) {
                        alert(`检测完成！处理了 ${result.total_detections} 个检测，发现 ${result.anomaly_count} 个异常`);
                        fetchBMemberData(); // 刷新数据
                    } else {
                        alert('检测失败: ' + result.error);
                    }
                } catch (error) {
                    alert('触发检测失败: ' + error.message);
                }
            });
        }

        // 生成演示数据按钮
        const demoBtn = document.getElementById('generate-demo-btn');
        if (demoBtn) {
            demoBtn.addEventListener('click', async () => {
                try {
                    const response = await fetch(`${backendUrl}/api/generate-demo`, {
                        method: 'POST'
                    });
                    const result = await response.json();
                    if (result.success) {
                        alert(`演示数据生成完成！总流量: ${result.total_flows}, 正常: ${result.normal_flows}, 攻击: ${result.attack_flows}`);
                        fetchBMemberData(); // 刷新数据
                    } else {
                        alert('生成演示数据失败: ' + result.error);
                    }
                } catch (error) {
                    alert('生成演示数据失败: ' + error.message);
                }
            });
        }
    }

    // --- 页面加载完成时调用初始数据加载函数 ---
    fetchTopology();
    fetchFlowTables();
    setupBMemberControls();

    // 定期刷新B成员数据
    setInterval(fetchBMemberData, 5000); // 每5秒刷新一次
});