document.addEventListener('DOMContentLoaded', () => {
    const backendUrl = 'http://localhost:3000';
    const wsUrl = 'ws://localhost:3000';

    // --- DOM 元素引用 ---
    const topologyContainer = document.getElementById('topology-container');
    const flowTableBody = document.getElementById('flow-table-body');
    const trafficChartDom = document.getElementById('traffic-chart');
    const alertList = document.getElementById('alert-list');

    // --- ECharts 流量图初始化 ---
    const trafficChart = echarts.init(trafficChartDom);
    let trafficXData = []; // 时间轴
    let trafficYData = []; // 流量值

    const trafficChartOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                params = params[0];
                return `时间: ${params.name}<br>流量: ${params.value} Mbps`;
            },
            axisPointer: {
                animation: false
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: trafficXData
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 200, // 根据实际流量范围调整
            splitLine: {
                show: true
            },
            name: '流量 (Mbps)'
        },
        series: [{
            name: '实时流量',
            type: 'line',
            showSymbol: false,
            data: trafficYData,
            lineStyle: {
                color: '#5470C6',
                width: 2
            },
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0,
                    color: 'rgba(84,112,198,0.5)'
                }, {
                    offset: 1,
                    color: 'rgba(84,112,198,0)'
                }])
            }
        }]
    };
    trafficChart.setOption(trafficChartOption);

    // --- 数据获取与渲染函数 ---

    // 获取并渲染网络拓扑
    async function fetchTopology() {
        try {
            const response = await fetch(`${backendUrl}/api/topology`);
            const data = await response.json();
            console.log('拓扑数据:', data);
            
            // 简单地以文本形式展示拓扑，你可以用D3.js等库绘制图形
            let topologyHtml = '<h3>节点:</h3><ul>';
            data.nodes.forEach(node => {
                topologyHtml += `<li>${node.name} (${node.type}, ${node.ip || node.dpId})</li>`;
            });
            topologyHtml += '</ul><h3>链路:</h3><ul>';
            data.links.forEach(link => {
                topologyHtml += `<li>${data.nodes.find(n => n.id === link.source)?.name} --- ${data.nodes.find(n => n.id === link.target)?.name}</li>`;
            });
            topologyHtml += '</ul>';
            topologyContainer.innerHTML = topologyHtml;

        } catch (error) {
            console.error('获取拓扑数据失败:', error);
            topologyContainer.innerHTML = '<p style="color: red;">加载拓扑失败。</p>';
        }
    }

    // 获取并渲染流表信息
    async function fetchFlowTables() {
        try {
            const response = await fetch(`${backendUrl}/api/flowtables`);
            const data = await response.json();
            console.log('流表数据:', data);

            flowTableBody.innerHTML = ''; // 清空现有内容
            if (data.length === 0) {
                flowTableBody.innerHTML = '<tr><td colspan="6">暂无流表信息。</td></tr>';
                return;
            }

            data.forEach(flow => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${flow.id}</td>
                    <td>${flow.dpId.substring(12)}</td> <!-- 简化DPID显示 -->
                    <td>${flow.match}</td>
                    <td>${flow.action}</td>
                    <td>${flow.packetCount}</td>
                    <td>${flow.byteCount}</td>
                `;
                flowTableBody.appendChild(row);
            });
        } catch (error) {
            console.error('获取流表数据失败:', error);
            flowTableBody.innerHTML = '<tr><td colspan="6" style="color: red;">加载流表失败。</td></tr>';
        }
    }

    // --- WebSocket 连接 ---
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
        console.log('WebSocket 连接成功');
    };

    ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        // console.log('收到 WebSocket 消息:', message);

        if (message.type === 'traffic') {
            const trafficData = message.data;
            // 更新 ECharts 图表数据
            trafficXData.push(new Date(trafficData.timestamp).toLocaleTimeString());
            trafficYData.push(trafficData.bandwidth);

            // 保持数据点数量，移除最旧的
            if (trafficXData.length > 100) { // 与后端 MAX_HISTORY_POINTS 保持一致
                trafficXData.shift();
                trafficYData.shift();
            }

            trafficChart.setOption({
                xAxis: {
                    data: trafficXData
                },
                series: [{
                    data: trafficYData
                }]
            });
        } else if (message.type === 'trafficHistory') {
            // 接收历史数据，填充图表
            message.data.forEach(item => {
                trafficXData.push(item.time);
                trafficYData.push(item.value);
            });
            trafficChart.setOption({
                xAxis: { data: trafficXData },
                series: [{ data: trafficYData }]
            });
        }
        else if (message.type === 'alert') {
            const alertData = message.data;
            const listItem = document.createElement('li');
            listItem.classList.add(`severity-${alertData.severity}`);
            listItem.innerHTML = `
                <strong>[${new Date(alertData.timestamp).toLocaleTimeString()}]</strong> 
                <strong>${alertData.type}</strong> 告警: ${alertData.source} -> ${alertData.destination} - ${alertData.description} 
                (严重性: ${alertData.severity})
            `;
            alertList.prepend(listItem); // 新告警显示在最顶部

            // 限制告警数量，避免列表过长
            if (alertList.children.length > 50) {
                alertList.lastChild.remove();
            }
        }
    };

    ws.onclose = () => {
        console.warn('WebSocket 连接已关闭');
    };

    ws.onerror = (error) => {
        console.error('WebSocket 错误:', error);
    };

    // --- 页面加载完成时调用初始数据加载函数 ---
    fetchTopology();
    fetchFlowTables();
});