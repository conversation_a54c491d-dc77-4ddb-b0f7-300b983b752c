"""
流量采集模块
Traffic Collector Module

功能：
- 模拟从SDN交换机采集流量数据
- 使用Scapy进行数据包捕获和分析
- 提供流量数据预处理功能
"""

import time
import numpy as np
from scapy.all import *
from collections import defaultdict, deque
import threading
import json

class TrafficCollector:
    """流量采集器类"""
    
    def __init__(self, interface=None, capture_duration=60):
        """
        初始化流量采集器
        
        Args:
            interface: 网络接口名称，None表示自动选择
            capture_duration: 采集持续时间（秒）
        """
        self.interface = interface
        self.capture_duration = capture_duration
        self.packets = []
        self.flow_stats = defaultdict(dict)
        self.is_collecting = False
        self.collection_thread = None
        
    def start_collection(self):
        """开始流量采集"""
        if self.is_collecting:
            print("流量采集已在进行中...")
            return
            
        self.is_collecting = True
        self.collection_thread = threading.Thread(target=self._collect_packets)
        self.collection_thread.start()
        print(f"开始流量采集，持续时间: {self.capture_duration}秒")
        
    def stop_collection(self):
        """停止流量采集"""
        self.is_collecting = False
        if self.collection_thread:
            self.collection_thread.join()
        print("流量采集已停止")
        
    def _collect_packets(self):
        """内部方法：采集数据包"""
        try:
            # 使用Scapy捕获数据包
            packets = sniff(
                iface=self.interface,
                timeout=self.capture_duration,
                stop_filter=lambda x: not self.is_collecting
            )
            self.packets = packets
            self._analyze_packets()
        except Exception as e:
            print(f"数据包采集错误: {e}")
            
    def _analyze_packets(self):
        """分析采集到的数据包"""
        for packet in self.packets:
            if IP in packet:
                src_ip = packet[IP].src
                dst_ip = packet[IP].dst
                protocol = packet[IP].proto
                packet_size = len(packet)
                timestamp = packet.time
                
                # 构建流标识
                flow_key = f"{src_ip}->{dst_ip}:{protocol}"
                
                # 更新流统计信息
                if flow_key not in self.flow_stats:
                    self.flow_stats[flow_key] = {
                        'packet_count': 0,
                        'byte_count': 0,
                        'start_time': timestamp,
                        'last_time': timestamp,
                        'packet_sizes': [],
                        'inter_arrival_times': []
                    }
                
                flow = self.flow_stats[flow_key]
                
                # 计算包间隔时间
                if flow['packet_count'] > 0:
                    inter_arrival = timestamp - flow['last_time']
                    flow['inter_arrival_times'].append(inter_arrival)
                
                # 更新统计信息
                flow['packet_count'] += 1
                flow['byte_count'] += packet_size
                flow['last_time'] = timestamp
                flow['packet_sizes'].append(packet_size)
                
    def get_flow_statistics(self):
        """获取流统计信息"""
        return dict(self.flow_stats)
        
    def get_traffic_summary(self):
        """获取流量摘要信息"""
        total_packets = len(self.packets)
        total_flows = len(self.flow_stats)
        total_bytes = sum(flow['byte_count'] for flow in self.flow_stats.values())
        
        return {
            'total_packets': total_packets,
            'total_flows': total_flows,
            'total_bytes': total_bytes,
            'collection_duration': self.capture_duration
        }
        
    def simulate_traffic_data(self, num_flows=100, duration=300):
        """
        模拟生成流量数据（用于测试）
        
        Args:
            num_flows: 模拟流的数量
            duration: 模拟时长（秒）
        """
        print(f"模拟生成 {num_flows} 个流的流量数据...")
        
        base_time = time.time()
        
        for i in range(num_flows):
            # 生成随机IP地址
            src_ip = f"192.168.1.{np.random.randint(1, 255)}"
            dst_ip = f"10.0.0.{np.random.randint(1, 255)}"
            protocol = np.random.choice([6, 17, 1])  # TCP, UDP, ICMP
            
            flow_key = f"{src_ip}->{dst_ip}:{protocol}"
            
            # 生成流特征
            packet_count = np.random.randint(10, 1000)
            packet_sizes = np.random.normal(500, 200, packet_count).astype(int)
            packet_sizes = np.clip(packet_sizes, 64, 1500)  # 限制包大小范围
            
            # 生成时间间隔
            inter_arrivals = np.random.exponential(0.1, packet_count-1)
            
            self.flow_stats[flow_key] = {
                'packet_count': packet_count,
                'byte_count': int(np.sum(packet_sizes)),
                'start_time': base_time + np.random.uniform(0, duration),
                'last_time': base_time + np.random.uniform(0, duration),
                'packet_sizes': packet_sizes.tolist(),
                'inter_arrival_times': inter_arrivals.tolist()
            }
            
        print(f"模拟数据生成完成，共 {len(self.flow_stats)} 个流")
        
    def export_data(self, filename):
        """导出采集的数据到文件"""
        data = {
            'flow_stats': dict(self.flow_stats),
            'summary': self.get_traffic_summary(),
            'export_time': time.time()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        print(f"数据已导出到: {filename}")

if __name__ == "__main__":
    # 测试代码
    collector = TrafficCollector()
    
    # 模拟生成测试数据
    collector.simulate_traffic_data(num_flows=50, duration=300)
    
    # 获取统计信息
    summary = collector.get_traffic_summary()
    print("流量摘要:", summary)
    
    # 导出数据
    collector.export_data("traffic_data.json")
