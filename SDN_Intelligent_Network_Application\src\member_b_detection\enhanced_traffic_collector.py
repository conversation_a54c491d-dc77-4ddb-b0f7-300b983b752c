#!/usr/bin/env python3
"""
增强型流量采集器 - B成员
实现高性能的SDN流量采集和预处理
整合原有traffic_collector.py的功能
"""

import time
import json
import threading
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import logging
import socket
import struct
import random
from datetime import datetime

# 尝试导入Scapy，如果失败则使用模拟模式
try:
    from scapy.all import *
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False
    print("Scapy未安装，使用模拟模式")

logger = logging.getLogger(__name__)

@dataclass
class FlowRecord:
    """流记录数据结构"""
    flow_id: str
    src_ip: str
    dst_ip: str
    src_port: int
    dst_port: int
    protocol: str
    start_time: float
    end_time: float
    packet_count: int
    byte_count: int
    packets: List[Dict[str, Any]]
    
    def __post_init__(self):
        if not self.packets:
            self.packets = []

@dataclass
class PacketInfo:
    """数据包信息"""
    timestamp: float
    size: int
    flags: str
    ttl: int
    window_size: int
    payload_size: int

class EnhancedTrafficCollector:
    """增强型流量采集器"""
    
    def __init__(self, collector_id: str = "main_collector"):
        """初始化采集器"""
        self.collector_id = collector_id
        self.flows: Dict[str, FlowRecord] = {}
        self.active_flows: Dict[str, FlowRecord] = {}
        self.completed_flows: deque = deque(maxlen=10000)
        
        # 采集配置
        self.flow_timeout = 60  # 流超时时间（秒）
        self.sampling_rate = 1.0  # 采样率
        self.max_packet_size = 1500  # 最大包大小
        
        # 运行状态
        self.is_collecting = False
        self.collection_thread = None
        self.cleanup_thread = None
        
        # 统计信息
        self.stats = {
            'total_packets': 0,
            'total_flows': 0,
            'active_flows': 0,
            'bytes_processed': 0,
            'start_time': time.time(),
            'packets_per_second': 0,
            'flows_per_second': 0
        }
        
        # 攻击模拟配置
        self.attack_scenarios = {
            'ddos': {'packet_rate': 10000, 'duration': 30},
            'port_scan': {'port_range': (1, 65535), 'scan_rate': 100},
            'data_exfiltration': {'data_rate': 1000000, 'duration': 60}
        }
        
        logger.info(f"增强型流量采集器 {collector_id} 初始化完成")
    
    def start_collection(self):
        """启动流量采集"""
        if self.is_collecting:
            logger.warning("流量采集已在运行")
            return
        
        self.is_collecting = True
        self.stats['start_time'] = time.time()
        
        # 启动采集线程
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        
        logger.info("流量采集已启动")
    
    def stop_collection(self):
        """停止流量采集"""
        self.is_collecting = False
        
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
            
        logger.info("流量采集已停止")
    
    def _collection_loop(self):
        """采集主循环"""
        last_stats_update = time.time()
        packet_count_window = deque(maxlen=100)
        flow_count_window = deque(maxlen=100)
        
        while self.is_collecting:
            try:
                # 模拟数据包采集
                packets = self._simulate_packet_capture()
                
                for packet in packets:
                    if random.random() <= self.sampling_rate:
                        self._process_packet(packet)
                        packet_count_window.append(time.time())
                
                # 更新统计信息
                current_time = time.time()
                if current_time - last_stats_update >= 1.0:
                    self._update_statistics(packet_count_window, flow_count_window)
                    last_stats_update = current_time
                
                time.sleep(0.01)  # 10ms间隔
                
            except Exception as e:
                logger.error(f"采集循环错误: {e}")
                time.sleep(1)
    
    def _simulate_packet_capture(self) -> List[Dict[str, Any]]:
        """模拟数据包捕获"""
        packets = []
        current_time = time.time()
        
        # 生成正常流量
        normal_packet_count = random.randint(10, 50)
        for _ in range(normal_packet_count):
            packet = self._generate_normal_packet(current_time)
            packets.append(packet)
        
        # 随机生成攻击流量
        if random.random() < 0.1:  # 10%概率生成攻击流量
            attack_type = random.choice(['ddos', 'port_scan', 'data_exfiltration'])
            attack_packets = self._generate_attack_packets(attack_type, current_time)
            packets.extend(attack_packets)
        
        return packets
    
    def _generate_normal_packet(self, timestamp: float) -> Dict[str, Any]:
        """生成正常数据包"""
        src_ips = ['*********', '*********', '*********', '*********']
        dst_ips = ['*********', '*********', '*********', '*********']
        protocols = ['TCP', 'UDP', 'ICMP']
        
        return {
            'timestamp': timestamp + random.uniform(-0.001, 0.001),
            'src_ip': random.choice(src_ips),
            'dst_ip': random.choice(dst_ips),
            'src_port': random.randint(1024, 65535),
            'dst_port': random.choice([80, 443, 22, 53, 25]),
            'protocol': random.choice(protocols),
            'size': random.randint(64, 1500),
            'flags': random.choice(['SYN', 'ACK', 'FIN', 'PSH']),
            'ttl': random.randint(32, 128),
            'window_size': random.randint(1024, 65535),
            'payload_size': random.randint(0, 1400)
        }
    
    def _generate_attack_packets(self, attack_type: str, timestamp: float) -> List[Dict[str, Any]]:
        """生成攻击数据包"""
        packets = []
        
        if attack_type == 'ddos':
            # DDoS攻击：大量小包，单一目标
            target_ip = '*********'
            attacker_ips = [f'192.168.{random.randint(1,254)}.{random.randint(1,254)}' 
                           for _ in range(20)]
            
            for _ in range(random.randint(100, 500)):
                packet = {
                    'timestamp': timestamp + random.uniform(0, 0.1),
                    'src_ip': random.choice(attacker_ips),
                    'dst_ip': target_ip,
                    'src_port': random.randint(1024, 65535),
                    'dst_port': 80,
                    'protocol': 'TCP',
                    'size': random.randint(40, 100),  # 小包
                    'flags': 'SYN',
                    'ttl': random.randint(1, 64),
                    'window_size': 0,
                    'payload_size': 0
                }
                packets.append(packet)
        
        elif attack_type == 'port_scan':
            # 端口扫描：单一源IP，多个目标端口
            scanner_ip = f'172.16.{random.randint(1,254)}.{random.randint(1,254)}'
            target_ip = '*********'
            
            for port in range(1, random.randint(100, 1000)):
                packet = {
                    'timestamp': timestamp + port * 0.001,
                    'src_ip': scanner_ip,
                    'dst_ip': target_ip,
                    'src_port': random.randint(1024, 65535),
                    'dst_port': port,
                    'protocol': 'TCP',
                    'size': 60,
                    'flags': 'SYN',
                    'ttl': 64,
                    'window_size': 1024,
                    'payload_size': 0
                }
                packets.append(packet)
        
        elif attack_type == 'data_exfiltration':
            # 数据泄露：大量数据传输
            insider_ip = '*********'
            external_ip = f'203.{random.randint(1,254)}.{random.randint(1,254)}.{random.randint(1,254)}'
            
            for _ in range(random.randint(50, 200)):
                packet = {
                    'timestamp': timestamp + random.uniform(0, 1),
                    'src_ip': insider_ip,
                    'dst_ip': external_ip,
                    'src_port': random.randint(1024, 65535),
                    'dst_port': 443,
                    'protocol': 'TCP',
                    'size': random.randint(1200, 1500),  # 大包
                    'flags': 'PSH',
                    'ttl': 64,
                    'window_size': 65535,
                    'payload_size': random.randint(1000, 1400)
                }
                packets.append(packet)
        
        return packets
    
    def _process_packet(self, packet: Dict[str, Any]):
        """处理单个数据包"""
        try:
            # 生成流ID
            flow_id = self._generate_flow_id(packet)
            
            # 更新或创建流记录
            if flow_id in self.active_flows:
                flow = self.active_flows[flow_id]
                flow.packet_count += 1
                flow.byte_count += packet['size']
                flow.end_time = packet['timestamp']
                flow.packets.append(packet)
            else:
                # 创建新流
                flow = FlowRecord(
                    flow_id=flow_id,
                    src_ip=packet['src_ip'],
                    dst_ip=packet['dst_ip'],
                    src_port=packet['src_port'],
                    dst_port=packet['dst_port'],
                    protocol=packet['protocol'],
                    start_time=packet['timestamp'],
                    end_time=packet['timestamp'],
                    packet_count=1,
                    byte_count=packet['size'],
                    packets=[packet]
                )
                self.active_flows[flow_id] = flow
                self.flows[flow_id] = flow
            
            # 更新统计
            self.stats['total_packets'] += 1
            self.stats['bytes_processed'] += packet['size']
            
        except Exception as e:
            logger.error(f"处理数据包失败: {e}")
    
    def _generate_flow_id(self, packet: Dict[str, Any]) -> str:
        """生成流ID"""
        # 使用五元组生成流ID
        src_ip = packet['src_ip']
        dst_ip = packet['dst_ip']
        src_port = packet['src_port']
        dst_port = packet['dst_port']
        protocol = packet['protocol']
        
        # 确保双向流使用相同ID
        if (src_ip, src_port) < (dst_ip, dst_port):
            return f"{src_ip}:{src_port}->{dst_ip}:{dst_port}_{protocol}"
        else:
            return f"{dst_ip}:{dst_port}->{src_ip}:{src_port}_{protocol}"
    
    def _cleanup_loop(self):
        """清理过期流"""
        while self.is_collecting:
            try:
                current_time = time.time()
                expired_flows = []
                
                for flow_id, flow in self.active_flows.items():
                    if current_time - flow.end_time > self.flow_timeout:
                        expired_flows.append(flow_id)
                
                for flow_id in expired_flows:
                    flow = self.active_flows.pop(flow_id)
                    self.completed_flows.append(flow)
                    logger.debug(f"流 {flow_id} 已过期，移至完成列表")
                
                time.sleep(10)  # 每10秒清理一次
                
            except Exception as e:
                logger.error(f"清理循环错误: {e}")
                time.sleep(30)
    
    def _update_statistics(self, packet_window: deque, flow_window: deque):
        """更新统计信息"""
        current_time = time.time()
        
        # 计算包速率
        recent_packets = [t for t in packet_window if current_time - t <= 1.0]
        self.stats['packets_per_second'] = len(recent_packets)
        
        # 计算流速率
        recent_flows = [t for t in flow_window if current_time - t <= 1.0]
        self.stats['flows_per_second'] = len(recent_flows)
        
        # 更新活跃流数量
        self.stats['active_flows'] = len(self.active_flows)
        self.stats['total_flows'] = len(self.flows)
    
    def get_flow_statistics(self) -> Dict[str, Dict]:
        """获取流统计信息，转换为字典格式供特征提取使用"""
        flow_stats = {}
        for flow_id, flow_record in self.flows.items():
            # 将FlowRecord转换为字典
            flow_stats[flow_id] = {
                'flow_id': flow_id,
                'packet_count': flow_record.packet_count,
                'byte_count': flow_record.byte_count,
                'start_time': flow_record.start_time,
                'last_time': flow_record.last_time,
                'packet_sizes': flow_record.packet_sizes,
                'inter_arrival_times': flow_record.inter_arrival_times,
                'flow_key': f"{flow_record.src_ip}->{flow_record.dst_ip}:{flow_record.protocol}"
            }
        return flow_stats
    
    def get_active_flows(self) -> Dict[str, FlowRecord]:
        """获取活跃流"""
        return self.active_flows.copy()
    
    def get_completed_flows(self, limit: int = 100) -> List[FlowRecord]:
        """获取已完成的流"""
        return list(self.completed_flows)[-limit:]
    
    def get_realtime_stats(self) -> Dict[str, Any]:
        """获取实时统计"""
        current_time = time.time()
        uptime = current_time - self.stats['start_time']
        
        return {
            'collector_id': self.collector_id,
            'is_collecting': self.is_collecting,
            'uptime': uptime,
            'total_packets': self.stats['total_packets'],
            'total_flows': self.stats['total_flows'],
            'active_flows': self.stats['active_flows'],
            'bytes_processed': self.stats['bytes_processed'],
            'packets_per_second': self.stats['packets_per_second'],
            'flows_per_second': self.stats['flows_per_second'],
            'avg_packet_size': self.stats['bytes_processed'] / max(1, self.stats['total_packets']),
            'timestamp': current_time
        }
    
    def simulate_attack_scenario(self, attack_type: str, duration: int = 30) -> bool:
        """模拟攻击场景"""
        try:
            if attack_type not in self.attack_scenarios:
                logger.error(f"未知攻击类型: {attack_type}")
                return False
            
            logger.info(f"开始模拟 {attack_type} 攻击，持续 {duration} 秒")
            
            def attack_generator():
                start_time = time.time()
                while time.time() - start_time < duration:
                    current_time = time.time()
                    attack_packets = self._generate_attack_packets(attack_type, current_time)
                    
                    for packet in attack_packets:
                        self._process_packet(packet)
                    
                    time.sleep(0.1)  # 100ms间隔
                
                logger.info(f"{attack_type} 攻击模拟完成")
            
            # 在后台线程中运行攻击模拟
            attack_thread = threading.Thread(target=attack_generator, daemon=True)
            attack_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"模拟攻击失败: {e}")
            return False
    
    def export_flow_data(self, format_type: str = 'json') -> str:
        """导出流数据"""
        try:
            if format_type == 'json':
                flow_data = []
                for flow in self.flows.values():
                    flow_dict = asdict(flow)
                    # 简化包数据以减少大小
                    flow_dict['packets'] = flow_dict['packets'][:10]  # 只保留前10个包
                    flow_data.append(flow_dict)
                
                return json.dumps(flow_data, indent=2, default=str)
            
            elif format_type == 'csv':
                flow_data = []
                for flow in self.flows.values():
                    flow_data.append({
                        'flow_id': flow.flow_id,
                        'src_ip': flow.src_ip,
                        'dst_ip': flow.dst_ip,
                        'src_port': flow.src_port,
                        'dst_port': flow.dst_port,
                        'protocol': flow.protocol,
                        'duration': flow.end_time - flow.start_time,
                        'packet_count': flow.packet_count,
                        'byte_count': flow.byte_count,
                        'avg_packet_size': flow.byte_count / max(1, flow.packet_count)
                    })
                
                df = pd.DataFrame(flow_data)
                return df.to_csv(index=False)
            
            else:
                raise ValueError(f"不支持的格式: {format_type}")
                
        except Exception as e:
            logger.error(f"导出流数据失败: {e}")
            return ""
    
    def get_flow_by_id(self, flow_id: str) -> Optional[FlowRecord]:
        """根据ID获取流"""
        return self.flows.get(flow_id)
    
    def get_flows_by_criteria(self, **criteria) -> List[FlowRecord]:
        """根据条件筛选流"""
        matching_flows = []
        
        for flow in self.flows.values():
            match = True
            
            for key, value in criteria.items():
                if hasattr(flow, key):
                    if getattr(flow, key) != value:
                        match = False
                        break
            
            if match:
                matching_flows.append(flow)
        
        return matching_flows
    
    def is_healthy(self) -> bool:
        """检查采集器健康状态"""
        return (self.is_collecting and 
                time.time() - self.stats['start_time'] > 0 and
                self.stats['total_packets'] > 0)

if __name__ == "__main__":
    # 测试代码
    collector = EnhancedTrafficCollector("test_collector")
    collector.start_collection()
    
    try:
        # 模拟攻击
        collector.simulate_attack_scenario('ddos', 10)
        
        time.sleep(15)
        
        stats = collector.get_realtime_stats()
        print(f"实时统计: {json.dumps(stats, indent=2)}")
        
        flows = collector.get_active_flows()
        print(f"活跃流数量: {len(flows)}")
        
    finally:
        collector.stop_collection()
