# B成员流量检测模块依赖包
# Traffic Detection Module Dependencies

# 核心科学计算库
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习库
scikit-learn>=1.0.0

# 网络数据包处理
scapy>=2.4.5

# 数据可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 数据处理和分析
python-dateutil>=2.8.0

# JSON处理（Python内置，但列出以确保兼容性）
# json - 内置模块

# 多线程和时间处理（Python内置）
# threading - 内置模块
# time - 内置模块
# collections - 内置模块

# 可选：如果需要更高级的网络功能
# netaddr>=0.8.0
# pyshark>=0.4.0

# 可选：如果需要更好的进度显示
# tqdm>=4.60.0

# 性能监控
psutil>=5.8.0
memory-profiler>=0.60.0

# 可选：如果需要配置文件支持
# pyyaml>=5.4.0
# configparser - 内置模块

# 可选：更高级的机器学习功能
# tensorflow>=2.8.0
# torch>=1.11.0
