#!/usr/bin/env python3
"""
API桥接脚本
用于为Node.js后端提供Python模块接口
"""

import sys
import json
import time
import os
from pathlib import Path

# 添加模块路径
sys.path.append(str(Path(__file__).parent))

from traffic_collector import TrafficCollector
from feature_extractor import FeatureExtractor
from dynamic_threshold import DynamicThreshold
from statistical_analyzer import StatisticalAnalyzer

class APIBridge:
    """API桥接类"""
    
    def __init__(self):
        """初始化桥接器"""
        self.collector = TrafficCollector()
        self.extractor = FeatureExtractor()
        self.detector = DynamicThreshold(window_size=50, sensitivity=2.0)
        self.analyzer = StatisticalAnalyzer()
        
        # 缓存数据
        self.cached_flow_stats = {}
        self.cached_detection_results = []
        self.last_update_time = 0
        
    def get_realtime_stats(self):
        """获取实时统计数据"""
        try:
            # 如果缓存数据过期，重新生成
            current_time = time.time()
            if current_time - self.last_update_time > 5:  # 5秒缓存
                self._update_cache()
                
            # 获取实时统计
            realtime_stats = self.collector.get_realtime_stats()
            
            # 添加检测相关统计
            detector_stats = self.detector.get_detection_stats()
            
            result = {
                'packets_per_second': realtime_stats.get('packets_per_second', 0),
                'flows_per_second': realtime_stats.get('flows_per_second', 0),
                'bytes_per_second': realtime_stats.get('bytes_per_second', 0),
                'anomaly_rate': detector_stats.get('anomaly_rate', 0),
                'current_threshold': detector_stats.get('current_threshold', 2.0),
                'total_flows': realtime_stats.get('total_flows', 0),
                'total_packets': realtime_stats.get('total_packets', 0),
                'total_bytes': realtime_stats.get('total_bytes', 0),
                'timestamp': current_time
            }
            
            return result
            
        except Exception as e:
            return {
                'error': str(e),
                'packets_per_second': 0,
                'flows_per_second': 0,
                'bytes_per_second': 0,
                'anomaly_rate': 0,
                'current_threshold': 2.0,
                'total_flows': 0
            }
            
    def get_detection_results(self, limit=10):
        """获取检测结果"""
        try:
            # 获取最近的检测结果
            recent_results = self.detector.get_recent_results(limit)
            
            # 格式化结果
            formatted_results = []
            for result in recent_results:
                formatted_result = {
                    'timestamp': result.get('timestamp', time.time()),
                    'value': result.get('value', 0),
                    'threshold': result.get('threshold', 0),
                    'is_anomaly': result.get('is_anomaly', False),
                    'anomaly_score': result.get('anomaly_score', 0),
                    'severity': self._calculate_severity(result.get('anomaly_score', 0))
                }
                formatted_results.append(formatted_result)
                
            # 统计信息
            anomaly_summary = {
                'total_detections': len(recent_results),
                'anomaly_count': sum(1 for r in recent_results if r.get('is_anomaly', False)),
                'avg_anomaly_score': sum(r.get('anomaly_score', 0) for r in recent_results) / len(recent_results) if recent_results else 0
            }
            
            return {
                'recent_detections': formatted_results,
                'anomaly_summary': anomaly_summary
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'recent_detections': [],
                'anomaly_summary': {}
            }
            
    def get_traffic_features(self, flow_id=None):
        """获取流量特征"""
        try:
            if not self.cached_flow_stats:
                self._update_cache()
                
            if flow_id and flow_id in self.cached_flow_stats:
                # 获取特定流的特征
                flow_data = self.cached_flow_stats[flow_id]
                features = self.extractor.extract_all_features(flow_data)
                
                return {
                    'flow_id': flow_id,
                    'features': features,
                    'feature_names': list(features.keys())
                }
            else:
                # 获取所有流的特征摘要
                if self.cached_flow_stats:
                    feature_matrix = self.extractor.extract_batch_features(self.cached_flow_stats)
                    
                    # 计算特征统计
                    feature_stats = {}
                    for column in feature_matrix.columns:
                        feature_stats[column] = {
                            'mean': float(feature_matrix[column].mean()),
                            'std': float(feature_matrix[column].std()),
                            'min': float(feature_matrix[column].min()),
                            'max': float(feature_matrix[column].max())
                        }
                    
                    return {
                        'feature_stats': feature_stats,
                        'feature_names': list(feature_matrix.columns),
                        'total_flows': len(self.cached_flow_stats)
                    }
                else:
                    return {
                        'feature_stats': {},
                        'feature_names': [],
                        'total_flows': 0
                    }
                    
        except Exception as e:
            return {
                'error': str(e),
                'features': {},
                'feature_names': []
            }
            
    def trigger_detection(self):
        """触发异常检测"""
        try:
            # 生成新的流量数据
            self.collector.simulate_traffic_data(num_flows=20, duration=60)
            self.collector.simulate_attack_traffic('ddos', num_flows=3)
            
            # 更新缓存
            self._update_cache()
            
            # 执行检测
            flow_stats = self.collector.get_flow_statistics()
            if flow_stats:
                # 提取特征并检测
                feature_matrix = self.extractor.extract_batch_features(flow_stats)
                packet_counts = feature_matrix['packet_count'].values
                
                # 批量检测
                results = self.detector.batch_detect(packet_counts)
                self.cached_detection_results = results
                
                return {
                    'success': True,
                    'message': f'检测完成，处理了 {len(flow_stats)} 个流',
                    'anomaly_count': sum(1 for r in results if r.get('is_anomaly', False)),
                    'total_detections': len(results)
                }
            else:
                return {
                    'success': False,
                    'message': '没有流量数据可供检测'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
            
    def generate_demo(self):
        """生成演示数据"""
        try:
            # 生成多种类型的流量
            self.collector.simulate_traffic_data(num_flows=30, duration=300)
            self.collector.simulate_attack_traffic('ddos', num_flows=5)
            self.collector.simulate_attack_traffic('port_scan', num_flows=3)
            self.collector.simulate_attack_traffic('data_exfiltration', num_flows=2)
            
            # 更新缓存
            self._update_cache()
            
            return {
                'success': True,
                'message': '演示数据生成完成',
                'total_flows': len(self.cached_flow_stats),
                'normal_flows': 30,
                'attack_flows': 10
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
            
    def get_performance_metrics(self):
        """获取性能指标"""
        try:
            # 模拟性能指标
            stats = self.detector.get_detection_stats()
            
            return {
                'processing_speed': 1000,  # flows/s
                'detection_accuracy': 0.95,
                'memory_usage': 45.2,  # MB
                'uptime': time.time() - self.last_update_time,
                'total_processed': stats.get('total_count', 0),
                'anomaly_rate': stats.get('anomaly_rate', 0)
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'processing_speed': 0,
                'detection_accuracy': 0,
                'memory_usage': 0,
                'uptime': 0
            }
            
    def _update_cache(self):
        """更新缓存数据"""
        try:
            # 如果没有数据，生成一些演示数据
            if not self.collector.get_flow_statistics():
                self.collector.simulate_traffic_data(num_flows=20, duration=180)
                self.collector.simulate_attack_traffic('ddos', num_flows=3)
                
            self.cached_flow_stats = self.collector.get_flow_statistics()
            self.last_update_time = time.time()
            
        except Exception as e:
            print(f"更新缓存失败: {e}", file=sys.stderr)
            
    def _calculate_severity(self, anomaly_score):
        """计算告警严重程度"""
        if anomaly_score > 3.0:
            return 'high'
        elif anomaly_score > 2.0:
            return 'medium'
        elif anomaly_score > 1.0:
            return 'low'
        else:
            return 'info'

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(json.dumps({'error': '缺少命令参数'}))
        sys.exit(1)
        
    command = sys.argv[1]
    bridge = APIBridge()
    
    try:
        if command == 'realtime_stats':
            result = bridge.get_realtime_stats()
        elif command == 'detection_results':
            limit = int(sys.argv[2]) if len(sys.argv) > 2 else 10
            result = bridge.get_detection_results(limit)
        elif command == 'traffic_features':
            flow_id = sys.argv[2] if len(sys.argv) > 2 else None
            result = bridge.get_traffic_features(flow_id)
        elif command == 'trigger_detection':
            result = bridge.trigger_detection()
        elif command == 'generate_demo':
            result = bridge.generate_demo()
        elif command == 'performance_metrics':
            result = bridge.get_performance_metrics()
        else:
            result = {'error': f'未知命令: {command}'}
            
        print(json.dumps(result, ensure_ascii=False))
        
    except Exception as e:
        print(json.dumps({'error': str(e)}, ensure_ascii=False))
        sys.exit(1)

if __name__ == '__main__':
    main()
