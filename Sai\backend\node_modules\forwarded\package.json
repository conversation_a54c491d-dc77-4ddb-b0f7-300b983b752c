{"_from": "forwarded@0.2.0", "_id": "forwarded@0.2.0", "_inBundle": false, "_integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "_location": "/forwarded", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "forwarded@0.2.0", "name": "forwarded", "escapedName": "forwarded", "rawSpec": "0.2.0", "saveSpec": null, "fetchSpec": "0.2.0"}, "_requiredBy": ["/proxy-addr"], "_resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "_shasum": "2269936428aad4c15c7ebe9779a84bf0b2a81811", "_spec": "forwarded@0.2.0", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\proxy-addr", "bugs": {"url": "https://github.com/jshttp/forwarded/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Parse HTTP X-Forwarded-For header", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "deep-equal": "1.0.1", "eslint": "7.27.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.23.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.4.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "homepage": "https://github.com/jshttp/forwarded#readme", "keywords": ["x-forwarded-for", "http", "req"], "license": "MIT", "name": "forwarded", "repository": {"type": "git", "url": "git+https://github.com/jshttp/forwarded.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "0.2.0"}