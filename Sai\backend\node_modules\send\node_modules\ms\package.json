{"_from": "ms@2.1.3", "_id": "ms@2.1.3", "_inBundle": false, "_integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "_location": "/send/ms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ms@2.1.3", "name": "ms", "escapedName": "ms", "rawSpec": "2.1.3", "saveSpec": null, "fetchSpec": "2.1.3"}, "_requiredBy": ["/send"], "_resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "_shasum": "574c8138ce1d2b5861f0b44579dbadd60c6615b2", "_spec": "ms@2.1.3", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\send", "bugs": {"url": "https://github.com/vercel/ms/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tiny millisecond conversion utility", "devDependencies": {"eslint": "4.18.2", "expect.js": "0.3.1", "husky": "0.14.3", "lint-staged": "5.0.0", "mocha": "4.0.1", "prettier": "2.0.5"}, "eslintConfig": {"extends": "eslint:recommended", "env": {"node": true, "es6": true}}, "files": ["index.js"], "homepage": "https://github.com/vercel/ms#readme", "license": "MIT", "lint-staged": {"*.js": ["npm run lint", "prettier --single-quote --write", "git add"]}, "main": "./index", "name": "ms", "repository": {"type": "git", "url": "git+https://github.com/vercel/ms.git"}, "scripts": {"lint": "eslint lib/* bin/*", "precommit": "lint-staged", "test": "mocha tests.js"}, "version": "2.1.3"}