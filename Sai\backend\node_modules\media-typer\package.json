{"_from": "media-typer@0.3.0", "_id": "media-typer@0.3.0", "_inBundle": false, "_integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "_location": "/media-typer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "media-typer@0.3.0", "name": "media-typer", "escapedName": "media-typer", "rawSpec": "0.3.0", "saveSpec": null, "fetchSpec": "0.3.0"}, "_requiredBy": ["/type-is"], "_resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "_shasum": "8710d7af0aa626f8fffa1ce00168545263255748", "_spec": "media-typer@0.3.0", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\type-is", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Simple RFC 6838 media type parser and formatter", "devDependencies": {"istanbul": "0.3.2", "mocha": "~1.21.4", "should": "~4.0.4"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/media-typer#readme", "license": "MIT", "name": "media-typer", "repository": {"type": "git", "url": "git+https://github.com/jshttp/media-typer.git"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "0.3.0"}