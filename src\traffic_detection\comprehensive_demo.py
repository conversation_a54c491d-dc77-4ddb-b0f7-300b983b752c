"""
综合演示脚本
Comprehensive Demo Script

功能：
- 展示完整的流量检测流程
- 演示所有增强功能
- 生成演示报告和图表
"""

import numpy as np
import time
import json
from datetime import datetime

from traffic_collector import TrafficCollector
from feature_extractor import FeatureExtractor
from dynamic_threshold import DynamicThreshold, HierarchicalThreshold, AdvancedAnomalyDetector
from statistical_analyzer import StatisticalAnalyzer

class TrafficDetectionDemo:
    """流量检测演示类"""
    
    def __init__(self):
        """初始化演示"""
        self.demo_results = {}
        
    def demo_traffic_collection(self):
        """演示流量采集功能"""
        print("=" * 60)
        print("🌐 流量采集演示")
        print("=" * 60)
        
        collector = TrafficCollector()
        
        # 1. 网络接口检测
        print("1. 检测可用网络接口...")
        interfaces = collector.get_available_interfaces()
        print(f"   发现 {len(interfaces)} 个网络接口")
        
        # 2. 生成正常流量
        print("\n2. 生成正常流量数据...")
        collector.simulate_traffic_data(num_flows=50, duration=300)
        
        # 3. 生成攻击流量
        print("\n3. 生成攻击流量数据...")
        ddos_flows = collector.simulate_attack_traffic('ddos', num_flows=10)
        scan_flows = collector.simulate_attack_traffic('port_scan', num_flows=5)
        exfil_flows = collector.simulate_attack_traffic('data_exfiltration', num_flows=3)
        
        # 4. 获取统计信息
        print("\n4. 流量统计信息:")
        summary = collector.get_traffic_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
            
        realtime_stats = collector.get_realtime_stats()
        print(f"   实时处理速度: {realtime_stats.get('flows_per_second', 0):.1f} flows/s")
        
        # 保存结果
        self.demo_results['traffic_collection'] = {
            'total_flows': len(collector.get_flow_statistics()),
            'normal_flows': 50,
            'attack_flows': {'ddos': 10, 'port_scan': 5, 'data_exfiltration': 3},
            'summary': summary
        }
        
        return collector.get_flow_statistics()
        
    def demo_feature_extraction(self, flow_stats):
        """演示特征提取功能"""
        print("\n" + "=" * 60)
        print("🔍 特征提取演示")
        print("=" * 60)
        
        extractor = FeatureExtractor()
        
        # 1. 单个流特征提取
        print("1. 单个流特征提取示例...")
        first_flow_key = list(flow_stats.keys())[0]
        first_flow = flow_stats[first_flow_key]
        
        features = extractor.extract_all_features(first_flow)
        print(f"   流标识: {first_flow_key}")
        print(f"   提取特征数量: {len(features)}")
        
        # 显示关键特征
        key_features = ['packet_count', 'entropy_packet_size', 'hurst_exponent', 'fractal_dimension']
        for feature in key_features:
            if feature in features:
                print(f"   {feature}: {features[feature]:.4f}")
        
        # 2. 批量特征提取
        print(f"\n2. 批量特征提取 ({len(flow_stats)} 个流)...")
        start_time = time.time()
        feature_matrix = extractor.extract_batch_features(flow_stats)
        extraction_time = time.time() - start_time
        
        print(f"   特征矩阵形状: {feature_matrix.shape}")
        print(f"   提取耗时: {extraction_time:.3f}s")
        print(f"   处理速度: {len(flow_stats)/extraction_time:.1f} flows/s")
        
        # 3. 特征选择
        print("\n3. 特征选择...")
        selected_features, selected_indices = extractor.feature_selection(
            feature_matrix, method='variance', threshold=0.01
        )
        print(f"   原始特征数: {feature_matrix.shape[1]}")
        print(f"   选择特征数: {len(selected_features)}")
        print(f"   特征选择率: {len(selected_features)/feature_matrix.shape[1]*100:.1f}%")
        
        # 4. 高级特征统计
        print("\n4. 高级特征统计...")
        hurst_values = feature_matrix['hurst_exponent'].values
        fractal_values = feature_matrix['fractal_dimension'].values
        
        print(f"   Hurst指数范围: [{np.min(hurst_values):.3f}, {np.max(hurst_values):.3f}]")
        print(f"   分形维数范围: [{np.min(fractal_values):.3f}, {np.max(fractal_values):.3f}]")
        
        # 保存结果
        self.demo_results['feature_extraction'] = {
            'total_features': feature_matrix.shape[1],
            'selected_features': len(selected_features),
            'extraction_time': extraction_time,
            'processing_speed': len(flow_stats)/extraction_time
        }
        
        return feature_matrix
        
    def demo_anomaly_detection(self, feature_matrix):
        """演示异常检测功能"""
        print("\n" + "=" * 60)
        print("🚨 异常检测演示")
        print("=" * 60)
        
        # 1. 动态阈值检测
        print("1. 动态阈值检测...")
        detector = DynamicThreshold(window_size=50, sensitivity=2.0)
        
        # 使用包数量作为检测特征
        packet_counts = feature_matrix['packet_count'].values
        
        start_time = time.time()
        detection_results = detector.batch_detect(packet_counts)
        detection_time = time.time() - start_time
        
        stats = detector.get_detection_stats()
        print(f"   检测数据点: {stats['total_count']}")
        print(f"   检测异常: {stats['anomaly_count']}")
        print(f"   异常率: {stats['anomaly_rate']:.2%}")
        print(f"   检测耗时: {detection_time:.3f}s")
        print(f"   检测速度: {stats['total_count']/detection_time:.1f} points/s")
        
        # 2. 分层阈值检测
        print("\n2. 分层阈值检测...")
        hierarchical_detector = HierarchicalThreshold()
        
        hierarchical_results = []
        for value in packet_counts[:20]:  # 测试前20个值
            result = hierarchical_detector.detect_multi_level(value)
            hierarchical_results.append(result)
            
        hierarchical_anomalies = [r for r in hierarchical_results if r['is_overall_anomaly']]
        print(f"   分层检测异常: {len(hierarchical_anomalies)}")
        
        if hierarchical_anomalies:
            avg_severity = np.mean([r['severity'] for r in hierarchical_anomalies])
            print(f"   平均严重程度: {avg_severity:.2f}")
        
        # 3. 高级异常检测
        print("\n3. 高级异常检测算法...")
        advanced_detector = AdvancedAnomalyDetector()
        
        # 选择部分特征进行检测
        selected_features = feature_matrix[['packet_count', 'byte_count', 'entropy_packet_size', 
                                         'hurst_exponent', 'fractal_dimension']].values
        
        # 孤立森林
        iso_scores, iso_preds = advanced_detector.isolation_forest_detection(selected_features)
        print(f"   孤立森林检测异常: {np.sum(iso_preds)}")
        
        # 单类SVM
        svm_scores, svm_preds = advanced_detector.one_class_svm_detection(selected_features)
        print(f"   单类SVM检测异常: {np.sum(svm_preds)}")
        
        # 集成检测
        ensemble_scores, ensemble_preds = advanced_detector.ensemble_detection(selected_features)
        print(f"   集成方法检测异常: {np.sum(ensemble_preds)}")
        
        # 保存结果
        self.demo_results['anomaly_detection'] = {
            'dynamic_threshold': {
                'anomaly_count': stats['anomaly_count'],
                'anomaly_rate': stats['anomaly_rate'],
                'detection_speed': stats['total_count']/detection_time
            },
            'hierarchical': {
                'anomaly_count': len(hierarchical_anomalies)
            },
            'advanced': {
                'isolation_forest': int(np.sum(iso_preds)),
                'one_class_svm': int(np.sum(svm_preds)),
                'ensemble': int(np.sum(ensemble_preds))
            }
        }
        
        return detection_results
        
    def demo_statistical_analysis(self, flow_stats, detection_results):
        """演示统计分析功能"""
        print("\n" + "=" * 60)
        print("📊 统计分析演示")
        print("=" * 60)
        
        analyzer = StatisticalAnalyzer()
        
        # 1. 流量模式分析
        print("1. 流量模式分析...")
        traffic_analysis = analyzer.analyze_traffic_patterns(flow_stats)
        
        print(f"   分析流数量: {traffic_analysis['flow_count']}")
        print(f"   平均包数量: {traffic_analysis['packet_stats']['mean']:.1f}")
        print(f"   包数量标准差: {traffic_analysis['packet_stats']['std']:.1f}")
        print(f"   总字节数: {traffic_analysis['byte_stats']['total']:,}")
        
        # 2. 检测性能分析
        print("\n2. 检测性能分析...")
        performance_analysis = analyzer.analyze_detection_performance(detection_results)
        
        print(f"   总检测次数: {performance_analysis['total_detections']}")
        print(f"   异常检测次数: {performance_analysis['anomaly_detections']}")
        print(f"   异常率: {performance_analysis['anomaly_rate']:.2%}")
        
        # 3. 时间序列分析
        print("\n3. 时间序列分析...")
        values = [r['value'] for r in detection_results]
        ts_analysis = analyzer.time_series_analysis(values)
        
        trend_info = ts_analysis.get('trend', {})
        seasonality_info = ts_analysis.get('seasonality', {})
        
        print(f"   趋势类型: {trend_info.get('trend_type', 'unknown')}")
        print(f"   趋势斜率: {trend_info.get('slope', 0):.6f}")
        print(f"   季节性: {seasonality_info.get('has_seasonality', False)}")
        
        # 4. 生成可视化图表
        print("\n4. 生成可视化图表...")
        try:
            analyzer.generate_traffic_visualization(flow_stats, "demo_traffic_analysis.png")
            analyzer.generate_threshold_visualization(detection_results, "demo_threshold_analysis.png")
            print("   ✅ 可视化图表生成成功")
        except Exception as e:
            print(f"   ⚠️ 可视化图表生成失败: {e}")
        
        # 5. 生成分析报告
        analyzer.generate_report("demo_analysis_report.json")
        print("   ✅ 分析报告已生成")
        
        # 保存结果
        self.demo_results['statistical_analysis'] = {
            'traffic_patterns': traffic_analysis,
            'detection_performance': performance_analysis,
            'time_series': ts_analysis
        }
        
        return analyzer
        
    def generate_demo_summary(self):
        """生成演示总结"""
        print("\n" + "=" * 60)
        print("📋 演示总结")
        print("=" * 60)
        
        # 计算总体性能指标
        total_flows = self.demo_results['traffic_collection']['total_flows']
        extraction_speed = self.demo_results['feature_extraction']['processing_speed']
        detection_speed = self.demo_results['anomaly_detection']['dynamic_threshold']['detection_speed']
        
        print(f"✅ 流量采集: 生成 {total_flows} 个流（包含正常和攻击流量）")
        print(f"✅ 特征提取: {extraction_speed:.1f} flows/s 处理速度")
        print(f"✅ 异常检测: {detection_speed:.1f} points/s 检测速度")
        print(f"✅ 统计分析: 完成流量模式和性能分析")
        
        # 检测效果总结
        anomaly_rate = self.demo_results['anomaly_detection']['dynamic_threshold']['anomaly_rate']
        print(f"✅ 检测效果: {anomaly_rate:.1%} 异常率")
        
        # 保存完整演示结果
        demo_summary = {
            'timestamp': datetime.now().isoformat(),
            'demo_results': self.demo_results,
            'summary': {
                'total_flows_processed': total_flows,
                'feature_extraction_speed': extraction_speed,
                'anomaly_detection_speed': detection_speed,
                'anomaly_detection_rate': anomaly_rate
            }
        }
        
        with open('demo_summary.json', 'w', encoding='utf-8') as f:
            json.dump(demo_summary, f, indent=2, ensure_ascii=False)
            
        print("\n📄 演示结果已保存到: demo_summary.json")
        
        return demo_summary
        
    def run_complete_demo(self):
        """运行完整演示"""
        print("🚀 开始B成员流量检测模块完整演示")
        print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 流量采集演示
            flow_stats = self.demo_traffic_collection()
            
            # 2. 特征提取演示
            feature_matrix = self.demo_feature_extraction(flow_stats)
            
            # 3. 异常检测演示
            detection_results = self.demo_anomaly_detection(feature_matrix)
            
            # 4. 统计分析演示
            analyzer = self.demo_statistical_analysis(flow_stats, detection_results)
            
            # 5. 生成演示总结
            summary = self.generate_demo_summary()
            
            print("\n🎉 完整演示成功完成！")
            print("\n📁 生成的文件:")
            print("   - demo_traffic_analysis.png: 流量分析图表")
            print("   - demo_threshold_analysis.png: 阈值检测图表")
            print("   - demo_analysis_report.json: 详细分析报告")
            print("   - demo_summary.json: 演示总结")
            
            return summary
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    demo = TrafficDetectionDemo()
    demo.run_complete_demo()
