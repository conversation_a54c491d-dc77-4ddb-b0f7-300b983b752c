#!/usr/bin/env python3
"""
网络拓扑管理模块 - A成员
负责SDN网络拓扑的发现、管理和维护
"""

import time
import json
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class NodeType(Enum):
    """节点类型枚举"""
    CONTROLLER = "controller"
    SWITCH = "switch"
    HOST = "host"
    ROUTER = "router"

class LinkType(Enum):
    """链路类型枚举"""
    CONTROL = "control"
    DATA = "data"
    MANAGEMENT = "management"

@dataclass
class NetworkNode:
    """网络节点数据结构"""
    node_id: str
    node_type: NodeType
    name: str
    ip_address: str
    mac_address: Optional[str] = None
    switch_dpid: Optional[str] = None
    ports: List[int] = None
    status: str = "active"
    last_seen: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.ports is None:
            self.ports = []
        if self.metadata is None:
            self.metadata = {}
        if self.last_seen == 0.0:
            self.last_seen = time.time()

@dataclass
class NetworkLink:
    """网络链路数据结构"""
    link_id: str
    link_type: LinkType
    src_node: str
    src_port: int
    dst_node: str
    dst_port: int
    bandwidth: int = 1000  # Mbps
    latency: float = 1.0   # ms
    packet_loss: float = 0.0  # %
    status: str = "active"
    last_seen: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.last_seen == 0.0:
            self.last_seen = time.time()

class TopologyManager:
    """网络拓扑管理器"""
    
    def __init__(self, controller_id: str = "main_controller"):
        """初始化拓扑管理器"""
        self.controller_id = controller_id
        self.nodes: Dict[str, NetworkNode] = {}
        self.links: Dict[str, NetworkLink] = {}
        
        # 拓扑发现配置
        self.discovery_interval = 10  # 秒
        self.node_timeout = 60  # 节点超时时间
        self.link_timeout = 60  # 链路超时时间
        
        # 运行状态
        self.is_running = False
        self.discovery_thread = None
        
        # 统计信息
        self.stats = {
            'total_nodes': 0,
            'active_nodes': 0,
            'total_links': 0,
            'active_links': 0,
            'last_discovery': 0.0
        }
        
        logger.info("网络拓扑管理器初始化完成")
    
    def start_discovery(self):
        """启动拓扑发现"""
        if self.is_running:
            logger.warning("拓扑发现已在运行")
            return
            
        self.is_running = True
        self.discovery_thread = threading.Thread(target=self._discovery_loop, daemon=True)
        self.discovery_thread.start()
        
        # 初始化基础拓扑
        self._initialize_base_topology()
        
        logger.info("拓扑发现已启动")
    
    def stop_discovery(self):
        """停止拓扑发现"""
        self.is_running = False
        if self.discovery_thread:
            self.discovery_thread.join(timeout=5)
        logger.info("拓扑发现已停止")
    
    def _discovery_loop(self):
        """拓扑发现主循环"""
        while self.is_running:
            try:
                self._discover_topology()
                self._cleanup_stale_entries()
                self._update_statistics()
                
                time.sleep(self.discovery_interval)
                
            except Exception as e:
                logger.error(f"拓扑发现循环错误: {e}")
                time.sleep(30)
    
    def _initialize_base_topology(self):
        """初始化基础拓扑结构"""
        # 添加控制器节点
        controller_node = NetworkNode(
            node_id="controller_main",
            node_type=NodeType.CONTROLLER,
            name="Main SDN Controller",
            ip_address="*************",
            ports=[6633, 6653],  # OpenFlow端口
            metadata={"version": "1.0", "vendor": "Custom"}
        )
        self.add_node(controller_node)
        
        # 添加模拟交换机
        switches = [
            {
                'node_id': 'switch_01',
                'name': 'Core Switch 1',
                'ip': '************',
                'dpid': '00:00:00:00:00:00:00:01',
                'ports': [1, 2, 3, 4, 5, 6]
            },
            {
                'node_id': 'switch_02', 
                'name': 'Core Switch 2',
                'ip': '************',
                'dpid': '00:00:00:00:00:00:00:02',
                'ports': [1, 2, 3, 4, 5, 6]
            },
            {
                'node_id': 'switch_03',
                'name': 'Edge Switch 1', 
                'ip': '************',
                'dpid': '00:00:00:00:00:00:00:03',
                'ports': [1, 2, 3, 4]
            }
        ]
        
        for switch_info in switches:
            switch_node = NetworkNode(
                node_id=switch_info['node_id'],
                node_type=NodeType.SWITCH,
                name=switch_info['name'],
                ip_address=switch_info['ip'],
                switch_dpid=switch_info['dpid'],
                ports=switch_info['ports'],
                metadata={"openflow_version": "1.3", "vendor": "OpenVSwitch"}
            )
            self.add_node(switch_node)
        
        # 添加模拟主机
        hosts = [
            {
                'node_id': 'host_01',
                'name': 'Server 1',
                'ip': '*********',
                'mac': '00:00:00:00:01:01',
                'connected_switch': 'switch_03',
                'connected_port': 1
            },
            {
                'node_id': 'host_02',
                'name': 'Server 2', 
                'ip': '*********',
                'mac': '00:00:00:00:01:02',
                'connected_switch': 'switch_03',
                'connected_port': 2
            },
            {
                'node_id': 'host_03',
                'name': 'Client 1',
                'ip': '*********', 
                'mac': '00:00:00:00:02:01',
                'connected_switch': 'switch_01',
                'connected_port': 5
            }
        ]
        
        for host_info in hosts:
            host_node = NetworkNode(
                node_id=host_info['node_id'],
                node_type=NodeType.HOST,
                name=host_info['name'],
                ip_address=host_info['ip'],
                mac_address=host_info['mac'],
                metadata={
                    "connected_switch": host_info['connected_switch'],
                    "connected_port": host_info['connected_port']
                }
            )
            self.add_node(host_node)
        
        # 添加控制链路
        control_links = [
            ('controller_main', 6633, 'switch_01', 0),
            ('controller_main', 6633, 'switch_02', 0),
            ('controller_main', 6633, 'switch_03', 0)
        ]
        
        for src, src_port, dst, dst_port in control_links:
            link = NetworkLink(
                link_id=f"ctrl_{src}_{dst}",
                link_type=LinkType.CONTROL,
                src_node=src,
                src_port=src_port,
                dst_node=dst,
                dst_port=dst_port,
                bandwidth=100,  # 控制链路带宽较小
                latency=0.5
            )
            self.add_link(link)
        
        # 添加数据链路
        data_links = [
            ('switch_01', 1, 'switch_02', 1),  # 核心交换机互联
            ('switch_01', 2, 'switch_03', 3),  # 核心到边缘
            ('switch_02', 2, 'switch_03', 4),  # 核心到边缘
            ('switch_03', 1, 'host_01', 0),    # 交换机到主机
            ('switch_03', 2, 'host_02', 0),    # 交换机到主机
            ('switch_01', 5, 'host_03', 0)     # 交换机到主机
        ]
        
        for src, src_port, dst, dst_port in data_links:
            link = NetworkLink(
                link_id=f"data_{src}_{dst}",
                link_type=LinkType.DATA,
                src_node=src,
                src_port=src_port,
                dst_node=dst,
                dst_port=dst_port,
                bandwidth=1000,
                latency=1.0
            )
            self.add_link(link)
        
        logger.info("基础拓扑结构初始化完成")
    
    def _discover_topology(self):
        """执行拓扑发现"""
        current_time = time.time()
        
        # 模拟动态拓扑变化
        self._simulate_topology_changes()
        
        # 更新所有节点的last_seen时间
        for node in self.nodes.values():
            if node.status == "active":
                node.last_seen = current_time
        
        # 更新所有链路的last_seen时间
        for link in self.links.values():
            if link.status == "active":
                link.last_seen = current_time
        
        self.stats['last_discovery'] = current_time
    
    def _simulate_topology_changes(self):
        """模拟拓扑变化（用于演示）"""
        import random
        
        # 随机模拟链路状态变化
        for link in self.links.values():
            if random.random() < 0.05:  # 5%概率发生变化
                if link.status == "active":
                    # 模拟链路故障
                    link.status = "down"
                    link.metadata["failure_reason"] = "simulated_failure"
                    logger.warning(f"链路 {link.link_id} 故障")
                elif link.status == "down" and random.random() < 0.3:
                    # 30%概率恢复
                    link.status = "active"
                    link.metadata.pop("failure_reason", None)
                    logger.info(f"链路 {link.link_id} 恢复")
        
        # 模拟带宽和延迟变化
        for link in self.links.values():
            if link.link_type == LinkType.DATA and random.random() < 0.1:
                # 模拟网络拥塞
                base_latency = 1.0
                congestion_factor = random.uniform(1.0, 3.0)
                link.latency = base_latency * congestion_factor
                link.metadata["congestion_level"] = congestion_factor
    
    def _cleanup_stale_entries(self):
        """清理过期的节点和链路"""
        current_time = time.time()
        
        # 清理过期节点
        stale_nodes = []
        for node_id, node in self.nodes.items():
            if current_time - node.last_seen > self.node_timeout:
                stale_nodes.append(node_id)
        
        for node_id in stale_nodes:
            logger.warning(f"节点 {node_id} 超时，标记为不活跃")
            self.nodes[node_id].status = "inactive"
        
        # 清理过期链路
        stale_links = []
        for link_id, link in self.links.items():
            if current_time - link.last_seen > self.link_timeout:
                stale_links.append(link_id)
        
        for link_id in stale_links:
            logger.warning(f"链路 {link_id} 超时，标记为不活跃")
            self.links[link_id].status = "inactive"
    
    def _update_statistics(self):
        """更新统计信息"""
        self.stats['total_nodes'] = len(self.nodes)
        self.stats['active_nodes'] = sum(1 for node in self.nodes.values() if node.status == "active")
        self.stats['total_links'] = len(self.links)
        self.stats['active_links'] = sum(1 for link in self.links.values() if link.status == "active")
    
    def add_node(self, node: NetworkNode) -> bool:
        """添加节点"""
        try:
            self.nodes[node.node_id] = node
            logger.info(f"添加节点: {node.node_id} ({node.node_type.value})")
            return True
        except Exception as e:
            logger.error(f"添加节点失败: {e}")
            return False
    
    def remove_node(self, node_id: str) -> bool:
        """移除节点"""
        try:
            if node_id in self.nodes:
                del self.nodes[node_id]
                # 同时移除相关链路
                related_links = [link_id for link_id, link in self.links.items() 
                               if link.src_node == node_id or link.dst_node == node_id]
                for link_id in related_links:
                    del self.links[link_id]
                logger.info(f"移除节点: {node_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"移除节点失败: {e}")
            return False
    
    def add_link(self, link: NetworkLink) -> bool:
        """添加链路"""
        try:
            # 验证链路端点存在
            if link.src_node not in self.nodes or link.dst_node not in self.nodes:
                logger.error(f"链路端点不存在: {link.src_node} -> {link.dst_node}")
                return False
            
            self.links[link.link_id] = link
            logger.info(f"添加链路: {link.link_id} ({link.link_type.value})")
            return True
        except Exception as e:
            logger.error(f"添加链路失败: {e}")
            return False
    
    def remove_link(self, link_id: str) -> bool:
        """移除链路"""
        try:
            if link_id in self.links:
                del self.links[link_id]
                logger.info(f"移除链路: {link_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"移除链路失败: {e}")
            return False
    
    def get_topology_data(self) -> Dict[str, Any]:
        """获取拓扑数据"""
        return {
            'nodes': [asdict(node) for node in self.nodes.values()],
            'links': [asdict(link) for link in self.links.values()],
            'stats': self.stats.copy(),
            'timestamp': time.time()
        }
    
    def get_shortest_path(self, src_node: str, dst_node: str) -> Optional[List[str]]:
        """计算最短路径（简单实现）"""
        try:
            # 使用BFS算法计算最短路径
            from collections import deque
            
            if src_node not in self.nodes or dst_node not in self.nodes:
                return None
            
            queue = deque([(src_node, [src_node])])
            visited = {src_node}
            
            while queue:
                current_node, path = queue.popleft()
                
                if current_node == dst_node:
                    return path
                
                # 查找相邻节点
                for link in self.links.values():
                    if link.status != "active":
                        continue
                        
                    next_node = None
                    if link.src_node == current_node:
                        next_node = link.dst_node
                    elif link.dst_node == current_node:
                        next_node = link.src_node
                    
                    if next_node and next_node not in visited:
                        visited.add(next_node)
                        queue.append((next_node, path + [next_node]))
            
            return None  # 无路径
            
        except Exception as e:
            logger.error(f"计算最短路径失败: {e}")
            return None
    
    def get_node_neighbors(self, node_id: str) -> List[str]:
        """获取节点的邻居"""
        neighbors = []
        try:
            for link in self.links.values():
                if link.status != "active":
                    continue
                    
                if link.src_node == node_id:
                    neighbors.append(link.dst_node)
                elif link.dst_node == node_id:
                    neighbors.append(link.src_node)
            
            return list(set(neighbors))  # 去重
            
        except Exception as e:
            logger.error(f"获取邻居节点失败: {e}")
            return []

if __name__ == "__main__":
    # 测试代码
    topology_manager = TopologyManager()
    topology_manager.start_discovery()
    
    try:
        time.sleep(5)
        topology_data = topology_manager.get_topology_data()
        print(f"拓扑数据: {json.dumps(topology_data, indent=2, default=str)}")
        
        # 测试路径计算
        path = topology_manager.get_shortest_path("host_01", "host_03")
        print(f"最短路径: {path}")
        
    finally:
        topology_manager.stop_discovery()
