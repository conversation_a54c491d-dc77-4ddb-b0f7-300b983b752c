{"_from": "methods@~1.1.2", "_id": "methods@1.1.2", "_inBundle": false, "_integrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==", "_location": "/methods", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "methods@~1.1.2", "name": "methods", "escapedName": "methods", "rawSpec": "~1.1.2", "saveSpec": null, "fetchSpec": "~1.1.2"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "_shasum": "5529a4d67654134edcc5266656835b0f851afcee", "_spec": "methods@~1.1.2", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\express", "browser": {"http": false}, "bugs": {"url": "https://github.com/jshttp/methods/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}], "deprecated": false, "description": "HTTP methods that node supports", "devDependencies": {"istanbul": "0.4.1", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "HISTORY.md", "LICENSE"], "homepage": "https://github.com/jshttp/methods#readme", "keywords": ["http", "methods"], "license": "MIT", "name": "methods", "repository": {"type": "git", "url": "git+https://github.com/jshttp/methods.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.1.2"}