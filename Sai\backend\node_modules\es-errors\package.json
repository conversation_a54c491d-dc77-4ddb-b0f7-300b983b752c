{"_from": "es-errors@^1.3.0", "_id": "es-errors@1.3.0", "_inBundle": false, "_integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "_location": "/es-errors", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "es-errors@^1.3.0", "name": "es-errors", "escapedName": "es-errors", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/call-bind-apply-helpers", "/dunder-proto", "/es-object-atoms", "/get-intrinsic", "/side-channel", "/side-channel-list", "/side-channel-map", "/side-channel-weakmap"], "_resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "_shasum": "05f75a25dab98e4fb1dcd5e1472c0546d5057c8f", "_spec": "es-errors@^1.3.0", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\side-channel", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A simple cache for a few of the JS Error constructors.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./eval": "./eval.js", "./range": "./range.js", "./ref": "./ref.js", "./syntax": "./syntax.js", "./type": "./type.js", "./uri": "./uri.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/es-errors#readme", "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "license": "MIT", "main": "index.js", "name": "es-errors", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.3.0"}