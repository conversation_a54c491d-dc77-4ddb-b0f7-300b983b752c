{"_from": "side-channel-weakmap@^1.0.2", "_id": "side-channel-weakmap@1.0.2", "_inBundle": false, "_integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "_location": "/side-channel-weakmap", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "side-channel-weakmap@^1.0.2", "name": "side-channel-weakmap", "escapedName": "side-channel-weakmap", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/side-channel"], "_resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "_shasum": "11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea", "_spec": "side-channel-weakmap@^1.0.2", "_where": "D:\\users\\Desktop\\VSCode\\.vscode\\Sai\\backend\\node_modules\\side-channel", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/side-channel-weakmap/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "deprecated": false, "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/side-channel-weakmap#readme", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "license": "MIT", "main": "index.js", "name": "side-channel-weakmap", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-weakmap.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "types": "./index.d.ts", "version": "1.0.2"}